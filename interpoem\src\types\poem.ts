export interface PoemVariable {
  name: string
  value: string | number | boolean
  type: 'string' | 'number' | 'boolean'
}

export interface PoemState {
  variables: Record<string, PoemVariable>
  currentSection: string
  audioContext: AudioContext | null
  isPlaying: boolean
  currentAudio: HTMLAudioElement | null
}

export interface MediaElement {
  type: 'audio' | 'image' | 'video'
  src: string
  alt?: string
  overlay?: string
  autoplay?: boolean
  loop?: boolean
  volume?: number
}

export interface InteractiveElement {
  type: 'hover' | 'click' | 'delay' | 'reveal'
  trigger?: string
  action?: string
  content?: string
  duration?: number
  effect?: string
}

export interface TextStyle {
  fontFamily?: string
  fontSize?: string
  fontWeight?: string
  color?: string
  background?: string
  textShadow?: string
  letterSpacing?: string
  lineHeight?: string
  transform?: string
  animation?: string
  gradient?: {
    type: 'linear' | 'radial' | 'conic'
    colors: string[]
    direction?: string
  }
  texture?: {
    type: 'watercolor' | 'gold' | 'paper' | 'ink'
    src?: string
    opacity?: number
  }
}

export interface WordElement {
  text: string
  style?: TextStyle
  interactive?: InteractiveElement
  media?: MediaElement
  id: string
  position: {
    line: number
    word: number
  }
}

export interface LineElement {
  words: WordElement[]
  style?: TextStyle
  alignment?: 'left' | 'center' | 'right' | 'justify'
  spacing?: number
  curve?: {
    radius: number
    angle: number
    direction: 'up' | 'down'
  }
  id: string
}

export interface VerseElement {
  lines: LineElement[]
  style?: TextStyle
  background?: {
    color?: string
    image?: string
    gradient?: TextStyle['gradient']
    overlay?: string
  }
  animation?: {
    type: 'fade' | 'slide' | 'typewriter' | 'float' | 'parallax'
    duration?: number
    delay?: number
    direction?: 'up' | 'down' | 'left' | 'right'
  }
  id: string
}

export interface PoemStructure {
  title?: string
  verses: VerseElement[]
  globalStyle?: TextStyle
  metadata?: {
    author?: string
    date?: string
    tags?: string[]
    description?: string
  }
}

export interface PoemCommand {
  type: 'set' | 'if' | 'endif' | 'media' | 'delay' | 'hover' | 'click'
  params: Record<string, any>
  content?: string
  condition?: string
}

export interface ParsedElement {
  type: 'text' | 'command' | 'media' | 'interactive'
  content: string
  commands?: PoemCommand[]
  style?: TextStyle
  interactive?: InteractiveElement
  media?: MediaElement
}

export interface AudioLayer {
  id: string
  src: string
  volume: number
  loop: boolean
  fadeIn?: number
  fadeOut?: number
  startTime?: number
  endTime?: number
}

export interface SoundscapeConfig {
  layers: AudioLayer[]
  masterVolume: number
  spatialAudio?: boolean
  reverb?: {
    roomSize: number
    dampening: number
  }
}

export interface ExportOptions {
  format: 'html' | 'pdf' | 'video' | 'app'
  includeAudio: boolean
  includeAnimations: boolean
  quality: 'low' | 'medium' | 'high'
  dimensions?: {
    width: number
    height: number
  }
}
