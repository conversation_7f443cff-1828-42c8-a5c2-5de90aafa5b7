'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '@/lib/utils'
import { WordElement } from '@/types/poem'

interface InteractiveWordProps {
  word: WordElement
  isRevealed: boolean
  onClick: (wordId: string, action?: string) => void
  onHover: (wordId: string, isHovering: boolean) => void
  className?: string
}

const InteractiveWord: React.FC<InteractiveWordProps> = ({
  word,
  isRevealed,
  onClick,
  onHover,
  className
}) => {
  const [isHovered, setIsHovered] = useState(false)
  const [showReveal, setShowReveal] = useState(false)
  const wordRef = useRef<HTMLSpanElement>(null)

  const handleMouseEnter = () => {
    setIsHovered(true)
    onHover(word.id, true)
    
    if (word.interactive?.type === 'hover') {
      setShowReveal(true)
    }
  }

  const handleMouseLeave = () => {
    setIsHovered(false)
    onHover(word.id, false)
    
    if (word.interactive?.type === 'hover') {
      setTimeout(() => setShowReveal(false), 200)
    }
  }

  const handleClick = () => {
    if (word.interactive?.type === 'click') {
      onClick(word.id, word.interactive.action)
    }
  }

  // Apply text styles
  const getTextStyle = () => {
    const style = word.style
    if (!style) return {}

    const textStyle: React.CSSProperties = {}

    if (style.fontFamily) textStyle.fontFamily = style.fontFamily
    if (style.fontSize) textStyle.fontSize = style.fontSize
    if (style.fontWeight) textStyle.fontWeight = style.fontWeight
    if (style.color) textStyle.color = style.color
    if (style.letterSpacing) textStyle.letterSpacing = style.letterSpacing
    if (style.lineHeight) textStyle.lineHeight = style.lineHeight
    if (style.textShadow) textStyle.textShadow = style.textShadow

    // Gradient text
    if (style.gradient) {
      const { type, colors, direction } = style.gradient
      const gradientDirection = direction || '45deg'
      textStyle.background = `${type}-gradient(${gradientDirection}, ${colors.join(', ')})`
      textStyle.WebkitBackgroundClip = 'text'
      textStyle.WebkitTextFillColor = 'transparent'
      textStyle.backgroundClip = 'text'
    }

    // Texture effects
    if (style.texture) {
      const { type, src, opacity } = style.texture
      if (type === 'watercolor' || src) {
        textStyle.backgroundImage = `url(${src || `/textures/${type}.png`})`
        textStyle.WebkitBackgroundClip = 'text'
        textStyle.WebkitTextFillColor = 'transparent'
        textStyle.backgroundClip = 'text'
        textStyle.backgroundSize = 'cover'
        if (opacity) textStyle.opacity = opacity
      }
    }

    return textStyle
  }

  // Check if word should be replaced with image
  const shouldReplaceWithImage = () => {
    return word.media?.type === 'image' && word.media.src
  }

  // Render image replacement
  const renderImageReplacement = () => {
    if (!word.media?.src) return null

    return (
      <motion.img
        src={`/images/${word.media.src}`}
        alt={word.media.alt || word.text}
        className={cn(
          "inline-block align-baseline",
          "max-h-[1.2em] w-auto", // Maintain line height
          isHovered && "scale-110 brightness-110"
        )}
        style={{
          filter: word.media.overlay ? `url(#${word.media.overlay})` : undefined
        }}
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
      />
    )
  }

  // Render text with texture inside letters
  const renderTexturedText = () => {
    if (!word.style?.texture) return word.text

    return (
      <span
        className="relative inline-block"
        style={{
          WebkitTextStroke: '1px currentColor',
          WebkitTextFillColor: 'transparent'
        }}
      >
        <span
          className="absolute inset-0"
          style={{
            backgroundImage: `url(/textures/${word.style.texture.type}.png)`,
            backgroundSize: 'cover',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent'
          }}
        >
          {word.text}
        </span>
        {word.text}
      </span>
    )
  }

  return (
    <motion.span
      ref={wordRef}
      data-word-id={word.id}
      className={cn(
        "word inline-block transition-all duration-300 cursor-pointer relative",
        word.interactive && "interactive-word",
        isHovered && "hovered",
        className
      )}
      style={getTextStyle()}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={handleClick}
      initial={{ opacity: 0, y: 10 }}
      animate={{ 
        opacity: isRevealed ? 1 : 0.7, 
        y: 0,
        scale: isHovered ? 1.05 : 1,
        rotateX: isHovered ? 5 : 0
      }}
      transition={{ 
        duration: 0.3,
        type: "spring",
        stiffness: 300,
        damping: 20
      }}
      whileHover={{
        scale: 1.05,
        textShadow: "0px 0px 8px rgba(0,0,0,0.3)"
      }}
    >
      {/* Main content */}
      {shouldReplaceWithImage() ? renderImageReplacement() : renderTexturedText()}

      {/* Hover reveal content */}
      <AnimatePresence>
        {showReveal && word.interactive?.content && (
          <motion.div
            className={cn(
              "absolute z-50 px-3 py-2 text-sm rounded-lg shadow-lg",
              "bg-black text-white dark:bg-white dark:text-black",
              "whitespace-nowrap pointer-events-none",
              "bottom-full left-1/2 transform -translate-x-1/2 mb-2"
            )}
            initial={{ opacity: 0, y: 10, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.8 }}
            transition={{ duration: 0.2 }}
          >
            {word.interactive.content}
            {/* Arrow */}
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black dark:border-t-white" />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Hidden art reveal */}
      <AnimatePresence>
        {isHovered && word.interactive?.type === 'reveal' && (
          <motion.div
            className="absolute inset-0 pointer-events-none"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            {/* Particle effects */}
            <div className="absolute inset-0 overflow-hidden">
              {[...Array(5)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-1 h-1 bg-current rounded-full"
                  initial={{ 
                    x: '50%', 
                    y: '50%', 
                    scale: 0 
                  }}
                  animate={{ 
                    x: `${50 + (Math.random() - 0.5) * 100}%`,
                    y: `${50 + (Math.random() - 0.5) * 100}%`,
                    scale: [0, 1, 0]
                  }}
                  transition={{ 
                    duration: 1,
                    delay: i * 0.1,
                    repeat: Infinity,
                    repeatDelay: 2
                  }}
                />
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Audio visualization */}
      {word.media?.type === 'audio' && isHovered && (
        <motion.div
          className="absolute -top-6 left-1/2 transform -translate-x-1/2"
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0 }}
        >
          <div className="audio-visualizer flex items-end gap-1">
            {[...Array(4)].map((_, i) => (
              <motion.div
                key={i}
                className="audio-bar bg-current"
                style={{ width: '2px' }}
                animate={{ 
                  height: [4, 12, 8, 16, 6],
                }}
                transition={{ 
                  duration: 0.5,
                  repeat: Infinity,
                  delay: i * 0.1
                }}
              />
            ))}
          </div>
        </motion.div>
      )}

      {/* Color change effects */}
      {isHovered && word.interactive?.effect === 'color-shift' && (
        <motion.div
          className="absolute inset-0 pointer-events-none"
          style={{
            background: 'linear-gradient(45deg, rgba(255,0,150,0.3), rgba(0,255,255,0.3))',
            mixBlendMode: 'multiply'
          }}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        />
      )}
    </motion.span>
  )
}

export default InteractiveWord
