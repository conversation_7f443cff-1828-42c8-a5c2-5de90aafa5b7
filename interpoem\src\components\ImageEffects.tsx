'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '@/lib/utils'

interface ImageEffectsProps {
  src: string
  alt?: string
  overlay?: 'watercolor' | 'ink' | 'paper' | 'torn' | 'splash' | string
  blendMode?: 'multiply' | 'screen' | 'overlay' | 'soft-light' | 'hard-light' | 'color-dodge' | 'color-burn'
  placement?: 'between-verses' | 'background' | 'inline' | 'floating'
  animation?: 'fade' | 'slide' | 'zoom' | 'parallax' | 'float'
  className?: string
  onLoad?: () => void
  onError?: (error: string) => void
}

const ImageEffects: React.FC<ImageEffectsProps> = ({
  src,
  alt = 'Poem illustration',
  overlay,
  blendMode = 'multiply',
  placement = 'between-verses',
  animation = 'fade',
  className,
  onLoad,
  onError
}) => {
  const [isLoaded, setIsLoaded] = useState(false)
  const [isVisible, setIsVisible] = useState(false)
  const imageRef = useRef<HTMLImageElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting)
      },
      { threshold: 0.1 }
    )

    if (containerRef.current) {
      observer.observe(containerRef.current)
    }

    return () => observer.disconnect()
  }, [])

  const handleImageLoad = () => {
    setIsLoaded(true)
    onLoad?.()
  }

  const handleImageError = () => {
    onError?.('Failed to load image')
  }

  const getAnimationVariants = () => {
    switch (animation) {
      case 'fade':
        return {
          initial: { opacity: 0 },
          animate: { opacity: isVisible ? 1 : 0 },
          transition: { duration: 1 }
        }
      case 'slide':
        return {
          initial: { opacity: 0, y: 50 },
          animate: { opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 50 },
          transition: { duration: 0.8, ease: 'easeOut' }
        }
      case 'zoom':
        return {
          initial: { opacity: 0, scale: 0.8 },
          animate: { opacity: isVisible ? 1 : 0, scale: isVisible ? 1 : 0.8 },
          transition: { duration: 0.6, ease: 'easeOut' }
        }
      case 'parallax':
        return {
          animate: { y: isVisible ? [0, -10, 0] : 0 },
          transition: { duration: 4, repeat: Infinity, ease: 'easeInOut' }
        }
      case 'float':
        return {
          animate: { y: [-5, 5, -5] },
          transition: { duration: 3, repeat: Infinity, ease: 'easeInOut' }
        }
      default:
        return {}
    }
  }

  const getOverlayElement = () => {
    if (!overlay) return null

    const overlayStyles: React.CSSProperties = {
      position: 'absolute',
      inset: 0,
      pointerEvents: 'none',
      mixBlendMode: blendMode
    }

    switch (overlay) {
      case 'watercolor':
        return (
          <div
            style={{
              ...overlayStyles,
              background: 'url(/textures/watercolor.svg)',
              backgroundSize: 'cover',
              opacity: 0.6
            }}
          />
        )
      case 'ink':
        return (
          <div
            style={{
              ...overlayStyles,
              background: 'radial-gradient(circle at 30% 70%, rgba(0,0,0,0.3) 0%, transparent 50%), radial-gradient(circle at 70% 30%, rgba(0,0,0,0.2) 0%, transparent 40%)',
              opacity: 0.4
            }}
          />
        )
      case 'paper':
        return (
          <div
            style={{
              ...overlayStyles,
              background: 'url(/textures/paper.svg)',
              backgroundSize: 'cover',
              opacity: 0.3
            }}
          />
        )
      case 'torn':
        return (
          <div
            style={{
              ...overlayStyles,
              background: 'linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.8) 32%, rgba(255,255,255,0.8) 68%, transparent 70%)',
              clipPath: 'polygon(0% 0%, 85% 0%, 90% 15%, 95% 25%, 100% 40%, 95% 60%, 90% 75%, 85% 100%, 0% 100%)'
            }}
          />
        )
      case 'splash':
        return (
          <div
            style={{
              ...overlayStyles,
              background: 'radial-gradient(ellipse at center, rgba(59,130,246,0.3) 0%, rgba(139,92,246,0.2) 30%, transparent 70%)',
              filter: 'blur(2px)'
            }}
          />
        )
      default:
        // Custom overlay (assume it's a URL or CSS)
        return (
          <div
            style={{
              ...overlayStyles,
              background: overlay.includes('url') ? overlay : `url(${overlay})`,
              backgroundSize: 'cover',
              opacity: 0.5
            }}
          />
        )
    }
  }

  const getPlacementStyles = () => {
    switch (placement) {
      case 'between-verses':
        return 'my-12 text-center'
      case 'background':
        return 'absolute inset-0 -z-10'
      case 'inline':
        return 'inline-block align-middle mx-2'
      case 'floating':
        return 'fixed top-1/2 right-8 transform -translate-y-1/2 z-20'
      default:
        return 'my-8 text-center'
    }
  }

  return (
    <motion.div
      ref={containerRef}
      className={cn(
        'image-effects relative overflow-hidden',
        getPlacementStyles(),
        className
      )}
      {...getAnimationVariants()}
    >
      {/* Main Image */}
      <motion.img
        ref={imageRef}
        src={`/images/${src}`}
        alt={alt}
        className={cn(
          'max-w-full h-auto object-cover transition-all duration-500',
          placement === 'inline' ? 'max-h-[1.5em] w-auto' : 'rounded-lg shadow-lg',
          isLoaded ? 'opacity-100' : 'opacity-0'
        )}
        onLoad={handleImageLoad}
        onError={handleImageError}
        whileHover={{
          scale: placement !== 'background' ? 1.02 : 1,
          filter: 'brightness(1.1)'
        }}
        transition={{ duration: 0.3 }}
      />

      {/* Overlay Effects */}
      <AnimatePresence>
        {isLoaded && getOverlayElement()}
      </AnimatePresence>

      {/* Decorative Elements */}
      {placement === 'between-verses' && (
        <motion.div
          className="absolute -top-4 -left-4 w-8 h-8 border-2 border-current opacity-20"
          style={{ borderRadius: '50% 0 50% 0' }}
          animate={{ rotate: 360 }}
          transition={{ duration: 20, repeat: Infinity, ease: 'linear' }}
        />
      )}

      {placement === 'between-verses' && (
        <motion.div
          className="absolute -bottom-4 -right-4 w-6 h-6 bg-current opacity-10"
          style={{ clipPath: 'polygon(50% 0%, 0% 100%, 100% 100%)' }}
          animate={{ scale: [1, 1.2, 1] }}
          transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
        />
      )}

      {/* Loading State */}
      {!isLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-muted/20 rounded-lg">
          <motion.div
            className="w-8 h-8 border-2 border-current border-t-transparent rounded-full"
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
          />
        </div>
      )}

      {/* Blend Mode Overlay for Text Integration */}
      {placement === 'inline' && (
        <div
          className="absolute inset-0 bg-current opacity-20"
          style={{ mixBlendMode: 'multiply' }}
        />
      )}
    </motion.div>
  )
}

export default ImageEffects
