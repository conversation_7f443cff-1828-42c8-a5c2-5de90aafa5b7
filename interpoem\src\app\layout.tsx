import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Interpoem - Interactive Poetry Platform',
  description: 'Create immersive, multimedia-rich poetry experiences with advanced typography, sound, and interactive elements.',
  keywords: ['poetry', 'interactive', 'multimedia', 'typography', 'art', 'sound'],
  authors: [{ name: 'Interpoem Team' }],
  viewport: 'width=device-width, initial-scale=1',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body className={`${inter.className} antialiased`}>
        <div id="root" className="min-h-screen bg-background text-foreground">
          {children}
        </div>
        <div id="portal-root" />
      </body>
    </html>
  )
}
