<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffd700;stop-opacity:1" />
      <stop offset="25%" style="stop-color:#ffed4e;stop-opacity:0.9" />
      <stop offset="50%" style="stop-color:#ffd700;stop-opacity:1" />
      <stop offset="75%" style="stop-color:#ffb700;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#ffd700;stop-opacity:1" />
    </linearGradient>
    <filter id="goldShine">
      <feGaussianBlur stdDeviation="1" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  <rect width="100%" height="100%" fill="url(#goldGradient)" filter="url(#goldShine)"/>
</svg>
