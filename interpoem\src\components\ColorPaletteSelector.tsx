'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Palette, Sun, Moon, Sparkles } from 'lucide-react'
import { cn } from '@/lib/utils'
import { ColorPaletteManager, predefinedPalettes } from '@/lib/colorPalette'
import { Button } from '@/components/ui/Button'

interface ColorPaletteSelectorProps {
  onPaletteChange: (paletteName: string, darkMode: boolean) => void
  className?: string
}

const ColorPaletteSelector: React.FC<ColorPaletteSelectorProps> = ({
  onPaletteChange,
  className
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const [selectedPalette, setSelectedPalette] = useState('ocean')
  const [isDarkMode, setIsDarkMode] = useState(false)
  const [paletteManager] = useState(new ColorPaletteManager())

  useEffect(() => {
    paletteManager.setPalette(selectedPalette)
    paletteManager.setDarkMode(isDarkMode)
    paletteManager.applyCSSVariables()
    onPaletteChange(selectedPalette, isDarkMode)
  }, [selectedPalette, isDarkMode, paletteManager, onPaletteChange])

  const handlePaletteSelect = (paletteName: string) => {
    setSelectedPalette(paletteName)
    setIsOpen(false)
  }

  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode)
  }

  const renderPalettePreview = (paletteName: string) => {
    const scheme = predefinedPalettes[paletteName]
    const palette = isDarkMode ? scheme.dark : scheme.light

    return (
      <motion.div
        key={`${paletteName}-${isDarkMode}`}
        className={cn(
          "palette-preview p-3 rounded-lg border-2 cursor-pointer transition-all",
          selectedPalette === paletteName 
            ? "border-primary shadow-lg scale-105" 
            : "border-transparent hover:border-muted-foreground/30"
        )}
        style={{ backgroundColor: palette.background }}
        onClick={() => handlePaletteSelect(paletteName)}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        {/* Palette Name */}
        <div 
          className="text-sm font-medium mb-2 capitalize"
          style={{ color: palette.text }}
        >
          {paletteName}
        </div>

        {/* Color Swatches */}
        <div className="flex gap-1 mb-2">
          <div 
            className="w-4 h-4 rounded-full border"
            style={{ backgroundColor: palette.primary }}
            title="Primary"
          />
          <div 
            className="w-4 h-4 rounded-full border"
            style={{ backgroundColor: palette.secondary }}
            title="Secondary"
          />
          <div 
            className="w-4 h-4 rounded-full border"
            style={{ backgroundColor: palette.accent }}
            title="Accent"
          />
        </div>

        {/* Gradient Preview */}
        <div 
          className="h-6 rounded border"
          style={{
            background: `linear-gradient(${palette.gradient.direction}, ${palette.gradient.start}, ${palette.gradient.end})`
          }}
        />

        {/* Mood Indicator */}
        <div className="flex items-center justify-between mt-2">
          <span 
            className="text-xs capitalize"
            style={{ color: palette.text }}
          >
            {palette.mood}
          </span>
          <Sparkles 
            className="w-3 h-3"
            style={{ color: palette.accent }}
          />
        </div>
      </motion.div>
    )
  }

  return (
    <div className={cn("relative", className)}>
      {/* Trigger Button */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2"
      >
        <Palette className="w-4 h-4" />
        <span className="hidden sm:inline">Colors</span>
      </Button>

      {/* Palette Selector Panel */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className={cn(
              "absolute top-full right-0 mt-2 p-4 bg-background border rounded-lg shadow-lg z-50",
              "w-80 max-h-96 overflow-y-auto"
            )}
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
          >
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-semibold">Color Palettes</h3>
              
              {/* Dark Mode Toggle */}
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleDarkMode}
                className="flex items-center gap-1"
              >
                {isDarkMode ? (
                  <>
                    <Moon className="w-4 h-4" />
                    <span className="text-xs">Dark</span>
                  </>
                ) : (
                  <>
                    <Sun className="w-4 h-4" />
                    <span className="text-xs">Light</span>
                  </>
                )}
              </Button>
            </div>

            {/* Palette Grid */}
            <div className="grid grid-cols-2 gap-3">
              {Object.keys(predefinedPalettes).map((paletteName) => 
                renderPalettePreview(paletteName)
              )}
            </div>

            {/* Current Palette Info */}
            <div className="mt-4 pt-4 border-t">
              <div className="text-sm text-muted-foreground mb-2">
                Current: <span className="font-medium capitalize">{selectedPalette}</span>
              </div>
              
              {/* Mood Effects Preview */}
              <div className="space-y-2">
                <div className="text-xs text-muted-foreground">Mood Effects:</div>
                <div 
                  className="text-sm p-2 rounded border"
                  style={{
                    ...paletteManager.getMoodEffects(),
                    color: paletteManager.getCurrentPalette().text,
                    backgroundColor: paletteManager.getCurrentPalette().background
                  }}
                >
                  Sample text with {paletteManager.getCurrentPalette().mood} mood
                </div>
              </div>
            </div>

            {/* Close Button */}
            <div className="mt-4 flex justify-end">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
              >
                Close
              </Button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Backdrop */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="fixed inset-0 z-40"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setIsOpen(false)}
          />
        )}
      </AnimatePresence>
    </div>
  )
}

export default ColorPaletteSelector
