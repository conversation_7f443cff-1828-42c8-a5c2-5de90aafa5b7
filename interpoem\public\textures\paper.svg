<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter id="paperTexture">
      <feTurbulence baseFrequency="0.9" numOctaves="4" result="noise"/>
      <feColorMatrix in="noise" type="saturate" values="0"/>
      <feComponentTransfer>
        <feFuncA type="discrete" tableValues="0.5 0.6 0.7 0.8"/>
      </feComponentTransfer>
      <feComposite operator="multiply" in2="SourceGraphic"/>
    </filter>
  </defs>
  <rect width="100%" height="100%" fill="#f8f6f0" filter="url(#paperTexture)"/>
</svg>
