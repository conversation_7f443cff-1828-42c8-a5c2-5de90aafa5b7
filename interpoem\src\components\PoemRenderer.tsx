'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { usePoemStore } from '@/stores/poemStore'
import { cn } from '@/lib/utils'
import { PoemParser } from '@/lib/poemParser'
import { AudioManager } from '@/lib/audioManager'
import InteractiveWord from '@/components/InteractiveWord'
import MediaElement from '@/components/MediaElement'
import { VerseElement } from '@/types/poem'

interface PoemRendererProps {
  poem: string
  className?: string
  autoPlay?: boolean
  showControls?: boolean
}

const PoemRenderer: React.FC<PoemRendererProps> = ({
  poem,
  className,
  autoPlay = false,
  showControls = true
}) => {
  const [parsedPoem, setParsedPoem] = useState<VerseElement[]>([])
  const [currentVerse, setCurrentVerse] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)
  const [revealedElements, setRevealedElements] = useState<Set<string>>(new Set())

  const containerRef = useRef<HTMLDivElement>(null)
  const audioManagerRef = useRef<AudioManager | null>(null)

  const {
    variables,
    setVariable,
    getVariable,
    initAudioContext,
    playAudio,
    stopAudio
  } = usePoemStore()

  // Initialize audio manager
  useEffect(() => {
    audioManagerRef.current = new AudioManager()
    initAudioContext()

    return () => {
      audioManagerRef.current?.cleanup()
    }
  }, [initAudioContext])

  // Parse poem when it changes
  useEffect(() => {
    try {
      const parser = new PoemParser()
      const parsed = parser.parse(poem)
      setParsedPoem(parsed.verses)

      // Reset state
      setCurrentVerse(0)
      setRevealedElements(new Set())

      if (autoPlay) {
        setIsPlaying(true)
      }
    } catch (error) {
      console.error('Error parsing poem:', error)
      setParsedPoem([])
    }
  }, [poem, autoPlay])

  // Handle scroll-based reveals
  useEffect(() => {
    const handleScroll = () => {
      if (!containerRef.current) return

      const elements = containerRef.current.querySelectorAll('[data-reveal-id]')
      elements.forEach((element) => {
        const rect = element.getBoundingClientRect()
        const isVisible = rect.top < window.innerHeight * 0.8

        if (isVisible) {
          const revealId = element.getAttribute('data-reveal-id')
          if (revealId) {
            setRevealedElements(prev => new Set([...prev, revealId]))
          }
        }
      })
    }

    window.addEventListener('scroll', handleScroll)
    handleScroll() // Check initial state

    return () => window.removeEventListener('scroll', handleScroll)
  }, [parsedPoem])

  const handleWordClick = useCallback((wordId: string, action?: string) => {
    if (action === 'audio') {
      // Handle audio playback
      const audioSrc = `/audio/${wordId}.mp3` // Example path
      playAudio(audioSrc)
    } else if (action === 'reveal') {
      setRevealedElements(prev => new Set([...prev, wordId]))
    }
  }, [playAudio])

  const handleHover = useCallback((wordId: string, isHovering: boolean) => {
    if (isHovering) {
      // Add hover effects, play sounds, etc.
      const element = document.querySelector(`[data-word-id="${wordId}"]`)
      if (element) {
        element.classList.add('hovered')
      }
    } else {
      const element = document.querySelector(`[data-word-id="${wordId}"]`)
      if (element) {
        element.classList.remove('hovered')
      }
    }
  }, [])

  const renderVerse = (verse: VerseElement, verseIndex: number) => {
    const isRevealed = revealedElements.has(verse.id) || verseIndex <= currentVerse

    return (
      <motion.div
        key={verse.id}
        data-reveal-id={verse.id}
        className={cn(
          "verse mb-12 p-6 rounded-lg",
          verse.style?.background?.color && `bg-${verse.style.background.color}`,
          isRevealed ? "opacity-100" : "opacity-30"
        )}
        initial={{ opacity: 0, y: 50 }}
        animate={{
          opacity: isRevealed ? 1 : 0.3,
          y: 0
        }}
        transition={{
          duration: 0.8,
          delay: verseIndex * 0.2,
          ease: "easeOut"
        }}
        style={{
          background: verse.style?.background?.gradient ?
            `linear-gradient(${verse.style.background.gradient.direction || '45deg'}, ${verse.style.background.gradient.colors.join(', ')})` :
            undefined
        }}
      >
        {verse.lines.map((line, lineIndex) => (
          <motion.div
            key={line.id}
            className={cn(
              "line mb-4",
              line.alignment === 'center' && "text-center",
              line.alignment === 'right' && "text-right"
            )}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{
              duration: 0.6,
              delay: (verseIndex * 0.2) + (lineIndex * 0.1)
            }}
            style={{
              transform: line.curve ?
                `rotate(${line.curve.angle}deg)` :
                undefined
            }}
          >
            {line.words.map((word, wordIndex) => (
              <InteractiveWord
                key={word.id}
                word={word}
                isRevealed={revealedElements.has(word.id)}
                onClick={handleWordClick}
                onHover={handleHover}
                className="mr-1"
              />
            ))}
          </motion.div>
        ))}
      </motion.div>
    )
  }

  if (!parsedPoem.length) {
    return (
      <div className={cn("flex items-center justify-center h-64", className)}>
        <div className="text-center">
          <div className="text-muted-foreground mb-2">No poem content</div>
          <div className="text-sm text-muted-foreground">
            Start writing in the editor to see your poem come to life
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn("poem-renderer relative", className)}>
      {/* Controls */}
      {showControls && (
        <div className="controls mb-6 flex items-center justify-between p-4 bg-muted/50 rounded-lg">
          <div className="flex items-center gap-4">
            <button
              onClick={() => setIsPlaying(!isPlaying)}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
            >
              {isPlaying ? 'Pause' : 'Play'}
            </button>
            
            <div className="text-sm text-muted-foreground">
              Verse {currentVerse + 1} of {parsedPoem.length}
            </div>
          </div>

          <div className="flex items-center gap-2">
            <button
              onClick={() => setCurrentVerse(Math.max(0, currentVerse - 1))}
              disabled={currentVerse === 0}
              className="px-3 py-1 text-sm border rounded disabled:opacity-50"
            >
              Previous
            </button>
            <button
              onClick={() => setCurrentVerse(Math.min(parsedPoem.length - 1, currentVerse + 1))}
              disabled={currentVerse === parsedPoem.length - 1}
              className="px-3 py-1 text-sm border rounded disabled:opacity-50"
            >
              Next
            </button>
          </div>
        </div>
      )}

      {/* Poem Content */}
      <div
        ref={containerRef}
        className="poem-content space-y-8 max-w-4xl mx-auto p-6"
      >
        <AnimatePresence mode="wait">
          {parsedPoem.map((verse, index) => renderVerse(verse, index))}
        </AnimatePresence>
      </div>
    </div>
  )
}

export default PoemRenderer
