'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { usePoemStore } from '@/stores/poemStore'
import { cn } from '@/lib/utils'

interface PoemRendererProps {
  poem: string
  className?: string
  autoPlay?: boolean
  showControls?: boolean
}

const PoemRenderer: React.FC<PoemRendererProps> = ({ 
  poem, 
  className,
  autoPlay = false,
  showControls = true
}) => {
  const [parsedLines, setParsedLines] = useState<string[]>([])
  const [currentLine, setCurrentLine] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)
  const [revealedElements, setRevealedElements] = useState<Set<string>>(new Set())
  
  const containerRef = useRef<HTMLDivElement>(null)
  
  const { 
    variables, 
    setVariable, 
    getVariable,
    initAudioContext,
    playAudio,
    stopAudio 
  } = usePoemStore()

  // Simple poem parser for now
  useEffect(() => {
    const lines = poem.split('\n').filter(line => line.trim())
    setParsedLines(lines)
    setCurrentLine(0)
    setRevealedElements(new Set())
    
    if (autoPlay) {
      setIsPlaying(true)
    }
  }, [poem, autoPlay])

  // Handle scroll-based reveals
  useEffect(() => {
    const handleScroll = () => {
      if (!containerRef.current) return

      const elements = containerRef.current.querySelectorAll('[data-reveal-id]')
      elements.forEach((element) => {
        const rect = element.getBoundingClientRect()
        const isVisible = rect.top < window.innerHeight * 0.8

        if (isVisible) {
          const revealId = element.getAttribute('data-reveal-id')
          if (revealId) {
            setRevealedElements(prev => new Set([...prev, revealId]))
          }
        }
      })
    }

    window.addEventListener('scroll', handleScroll)
    handleScroll() // Check initial state

    return () => window.removeEventListener('scroll', handleScroll)
  }, [parsedLines])

  const processLine = (line: string, index: number) => {
    // Handle variables
    if (line.includes('<<set')) {
      const match = line.match(/<<set\s+(\w+)="([^"]*)">>/)
      if (match) {
        setVariable(match[1], match[2])
        return null // Don't render set commands
      }
    }

    // Handle conditionals
    if (line.includes('<<if')) {
      const match = line.match(/<<if\s+(\w+)=="([^"]*)">>/)
      if (match) {
        const variable = getVariable(match[1])
        if (!variable || variable.value !== match[2]) {
          return null // Don't render if condition fails
        }
        return null // Don't render the if statement itself
      }
    }

    // Handle media
    if (line.includes('[[audio:')) {
      const match = line.match(/\[\[audio:([^\]]+)\]\]/)
      if (match) {
        return (
          <div key={`audio-${index}`} className="audio-element my-4">
            <button
              onClick={() => playAudio(`/audio/${match[1]}`)}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
            >
              🎵 Play Audio
            </button>
          </div>
        )
      }
    }

    if (line.includes('[[image:')) {
      const match = line.match(/\[\[image:([^\]]+)\]\]/)
      if (match) {
        return (
          <div key={`image-${index}`} className="image-element my-6 text-center">
            <img 
              src={`/images/${match[1]}`} 
              alt="Poem illustration"
              className="max-w-full h-auto rounded-lg shadow-lg mx-auto"
            />
          </div>
        )
      }
    }

    if (line.includes('[[delay:')) {
      const match = line.match(/\[\[delay:(\d+)\]\]/)
      if (match) {
        return (
          <div key={`delay-${index}`} className="delay-element my-2">
            <div className="text-center text-muted-foreground text-sm">
              ⏱️ Pause ({parseInt(match[1]) / 1000}s)
            </div>
          </div>
        )
      }
    }

    // Handle hover effects
    if (line.includes('[[hover:')) {
      const match = line.match(/\[\[hover:reveal="([^"]+)"\]\](.+?)\[\[\/hover\]\]/)
      if (match) {
        return (
          <div key={`hover-${index}`} className="hover-element inline-block">
            <span 
              className="interactive-word cursor-pointer relative group"
              title={match[1]}
            >
              {match[2]}
              <span className="absolute bottom-full left-1/2 transform -translate-x-1/2 bg-black text-white text-xs rounded px-2 py-1 opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                {match[1]}
              </span>
            </span>
          </div>
        )
      }
    }

    // Handle headers
    if (line.startsWith('#')) {
      const level = line.match(/^#+/)?.[0].length || 1
      const text = line.replace(/^#+\s*/, '')
      const HeaderTag = `h${Math.min(level, 6)}` as keyof JSX.IntrinsicElements
      
      return (
        <HeaderTag 
          key={`header-${index}`}
          className={cn(
            "font-bold mb-4",
            level === 1 && "text-4xl text-center bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent",
            level === 2 && "text-3xl",
            level === 3 && "text-2xl"
          )}
        >
          {text}
        </HeaderTag>
      )
    }

    // Handle italic text
    if (line.includes('*') && !line.startsWith('*')) {
      const processedLine = line.replace(/\*([^*]+)\*/g, '<em class="italic text-blue-600">$1</em>')
      return (
        <p 
          key={`line-${index}`}
          className="line mb-3 leading-relaxed"
          dangerouslySetInnerHTML={{ __html: processedLine }}
        />
      )
    }

    // Regular text
    return (
      <p key={`line-${index}`} className="line mb-3 leading-relaxed">
        {line}
      </p>
    )
  }

  const renderLine = (line: string, index: number) => {
    const isRevealed = revealedElements.has(`line-${index}`) || index <= currentLine
    const processedElement = processLine(line, index)
    
    if (!processedElement) return null

    return (
      <motion.div
        key={`container-${index}`}
        data-reveal-id={`line-${index}`}
        initial={{ opacity: 0, y: 20 }}
        animate={{ 
          opacity: isRevealed ? 1 : 0.3, 
          y: 0 
        }}
        transition={{ 
          duration: 0.6, 
          delay: index * 0.1,
          ease: "easeOut"
        }}
      >
        {processedElement}
      </motion.div>
    )
  }

  if (!parsedLines.length) {
    return (
      <div className={cn("flex items-center justify-center h-64", className)}>
        <div className="text-center">
          <div className="text-muted-foreground mb-2">No poem content</div>
          <div className="text-sm text-muted-foreground">
            Start writing in the editor to see your poem come to life
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn("poem-renderer relative", className)}>
      {/* Controls */}
      {showControls && (
        <div className="controls mb-6 flex items-center justify-between p-4 bg-muted/50 rounded-lg">
          <div className="flex items-center gap-4">
            <button
              onClick={() => setIsPlaying(!isPlaying)}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
            >
              {isPlaying ? 'Pause' : 'Play'}
            </button>
            
            <div className="text-sm text-muted-foreground">
              Line {currentLine + 1} of {parsedLines.length}
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={() => setCurrentLine(Math.max(0, currentLine - 1))}
              disabled={currentLine === 0}
              className="px-3 py-1 text-sm border rounded disabled:opacity-50"
            >
              Previous
            </button>
            <button
              onClick={() => setCurrentLine(Math.min(parsedLines.length - 1, currentLine + 1))}
              disabled={currentLine === parsedLines.length - 1}
              className="px-3 py-1 text-sm border rounded disabled:opacity-50"
            >
              Next
            </button>
          </div>
        </div>
      )}

      {/* Poem Content */}
      <div 
        ref={containerRef}
        className="poem-content space-y-4 max-w-4xl mx-auto p-6"
      >
        <AnimatePresence mode="wait">
          {parsedLines.map((line, index) => renderLine(line, index))}
        </AnimatePresence>
      </div>
    </div>
  )
}

export default PoemRenderer
