'use client'

import { useRef, useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'
import { TextStyle } from '@/types/poem'

interface TypographyRendererProps {
  text: string
  style?: TextStyle
  className?: string
  curvedPath?: {
    radius: number
    angle: number
    direction: 'up' | 'down'
  }
  animation?: {
    type: 'typewriter' | 'fade' | 'slide' | 'float' | 'wave'
    duration?: number
    delay?: number
  }
  layers?: TextStyle[]
}

const TypographyRenderer: React.FC<TypographyRendererProps> = ({
  text,
  style,
  className,
  curvedPath,
  animation,
  layers = []
}) => {
  const textRef = useRef<HTMLDivElement>(null)
  const svgRef = useRef<SVGSVGElement>(null)
  const [pathId] = useState(`path-${Math.random().toString(36).substr(2, 9)}`)

  // Create curved path for text
  useEffect(() => {
    if (curvedPath && svgRef.current) {
      const { radius, angle, direction } = curvedPath
      const startAngle = -angle / 2
      const endAngle = angle / 2
      
      const startX = radius * Math.cos((startAngle * Math.PI) / 180)
      const startY = radius * Math.sin((startAngle * Math.PI) / 180) * (direction === 'up' ? -1 : 1)
      const endX = radius * Math.cos((endAngle * Math.PI) / 180)
      const endY = radius * Math.sin((endAngle * Math.PI) / 180) * (direction === 'up' ? -1 : 1)
      
      const largeArcFlag = angle > 180 ? 1 : 0
      const sweepFlag = direction === 'up' ? 0 : 1
      
      const pathData = `M ${startX + radius} ${startY + radius} A ${radius} ${radius} 0 ${largeArcFlag} ${sweepFlag} ${endX + radius} ${endY + radius}`
      
      const pathElement = svgRef.current.querySelector('path')
      if (pathElement) {
        pathElement.setAttribute('d', pathData)
      }
    }
  }, [curvedPath])

  // Generate animation variants
  const getAnimationVariants = () => {
    if (!animation) return {}

    const { type, duration = 1, delay = 0 } = animation

    switch (type) {
      case 'typewriter':
        return {
          initial: { width: 0 },
          animate: { width: '100%' },
          transition: { duration, delay, ease: 'steps(40, end)' }
        }
      case 'fade':
        return {
          initial: { opacity: 0 },
          animate: { opacity: 1 },
          transition: { duration, delay }
        }
      case 'slide':
        return {
          initial: { x: -50, opacity: 0 },
          animate: { x: 0, opacity: 1 },
          transition: { duration, delay, type: 'spring' }
        }
      case 'float':
        return {
          animate: { y: [-5, 5, -5] },
          transition: { duration: 2, repeat: Infinity, ease: 'easeInOut' }
        }
      case 'wave':
        return {
          animate: { 
            rotateX: [0, 10, 0, -10, 0],
            rotateY: [0, 5, 0, -5, 0]
          },
          transition: { duration: 3, repeat: Infinity, ease: 'easeInOut' }
        }
      default:
        return {}
    }
  }

  // Render text with multiple layers
  const renderLayeredText = () => {
    if (layers.length === 0) {
      return <span>{text}</span>
    }

    return (
      <span className="relative inline-block">
        {layers.map((layer, index) => (
          <span
            key={index}
            className={cn(
              "absolute inset-0",
              index > 0 && "mix-blend-multiply"
            )}
            style={{
              ...getStyleFromTextStyle(layer),
              zIndex: layers.length - index
            }}
          >
            {text}
          </span>
        ))}
        <span className="relative z-10" style={getStyleFromTextStyle(style)}>
          {text}
        </span>
      </span>
    )
  }

  // Convert TextStyle to CSS style object
  const getStyleFromTextStyle = (textStyle?: TextStyle): React.CSSProperties => {
    if (!textStyle) return {}

    const cssStyle: React.CSSProperties = {}

    if (textStyle.fontFamily) cssStyle.fontFamily = textStyle.fontFamily
    if (textStyle.fontSize) cssStyle.fontSize = textStyle.fontSize
    if (textStyle.fontWeight) cssStyle.fontWeight = textStyle.fontWeight
    if (textStyle.color) cssStyle.color = textStyle.color
    if (textStyle.letterSpacing) cssStyle.letterSpacing = textStyle.letterSpacing
    if (textStyle.lineHeight) cssStyle.lineHeight = textStyle.lineHeight
    if (textStyle.textShadow) cssStyle.textShadow = textStyle.textShadow

    // Gradient effects
    if (textStyle.gradient) {
      const { type, colors, direction } = textStyle.gradient
      const gradientDirection = direction || '45deg'
      cssStyle.background = `${type}-gradient(${gradientDirection}, ${colors.join(', ')})`
      cssStyle.WebkitBackgroundClip = 'text'
      cssStyle.WebkitTextFillColor = 'transparent'
      cssStyle.backgroundClip = 'text'
    }

    // Texture effects
    if (textStyle.texture) {
      const { type, src, opacity } = textStyle.texture
      cssStyle.backgroundImage = `url(${src || `/textures/${type}.png`})`
      cssStyle.WebkitBackgroundClip = 'text'
      cssStyle.WebkitTextFillColor = 'transparent'
      cssStyle.backgroundClip = 'text'
      cssStyle.backgroundSize = 'cover'
      if (opacity) cssStyle.opacity = opacity
    }

    return cssStyle
  }

  // Render curved text using SVG
  if (curvedPath) {
    return (
      <div className={cn("relative inline-block", className)}>
        <svg
          ref={svgRef}
          width={curvedPath.radius * 2}
          height={curvedPath.radius * 2}
          className="overflow-visible"
        >
          <defs>
            <path id={pathId} />
          </defs>
          <text
            className="fill-current"
            style={getStyleFromTextStyle(style)}
          >
            <textPath href={`#${pathId}`} startOffset="50%" textAnchor="middle">
              {text}
            </textPath>
          </text>
        </svg>
      </div>
    )
  }

  // Regular text with animations and effects
  return (
    <motion.div
      ref={textRef}
      className={cn("typography-renderer", className)}
      {...getAnimationVariants()}
    >
      {animation?.type === 'typewriter' ? (
        <div className="overflow-hidden whitespace-nowrap">
          <motion.div
            className="inline-block"
            style={getStyleFromTextStyle(style)}
            {...getAnimationVariants()}
          >
            {renderLayeredText()}
          </motion.div>
        </div>
      ) : (
        <span style={getStyleFromTextStyle(style)}>
          {renderLayeredText()}
        </span>
      )}
    </motion.div>
  )
}

export default TypographyRenderer
