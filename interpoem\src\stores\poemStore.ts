import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { PoemState, PoemVariable, AudioLayer } from '@/types/poem'

interface PoemStore extends PoemState {
  // Variable management
  setVariable: (name: string, value: string | number | boolean, type?: 'string' | 'number' | 'boolean') => void
  getVariable: (name: string) => PoemVariable | undefined
  clearVariables: () => void
  
  // Audio management
  initAudioContext: () => void
  playAudio: (src: string, options?: { loop?: boolean; volume?: number }) => Promise<void>
  stopAudio: () => void
  setMasterVolume: (volume: number) => void
  addAudioLayer: (layer: AudioLayer) => void
  removeAudioLayer: (id: string) => void
  
  // Navigation
  setCurrentSection: (section: string) => void
  
  // State management
  reset: () => void
}

const initialState: PoemState = {
  variables: {},
  currentSection: 'main',
  audioContext: null,
  isPlaying: false,
  currentAudio: null,
}

export const usePoemStore = create<PoemStore>()(
  immer((set, get) => ({
    ...initialState,
    
    setVariable: (name, value, type = 'string') => {
      set((state) => {
        state.variables[name] = { name, value, type }
      })
    },
    
    getVariable: (name) => {
      return get().variables[name]
    },
    
    clearVariables: () => {
      set((state) => {
        state.variables = {}
      })
    },
    
    initAudioContext: () => {
      set((state) => {
        if (!state.audioContext) {
          state.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
        }
      })
    },
    
    playAudio: async (src, options = {}) => {
      const { loop = false, volume = 1 } = options
      
      set((state) => {
        // Stop current audio if playing
        if (state.currentAudio) {
          state.currentAudio.pause()
          state.currentAudio.currentTime = 0
        }
        
        // Create new audio element
        const audio = new Audio(src)
        audio.loop = loop
        audio.volume = volume
        
        audio.play().then(() => {
          state.currentAudio = audio
          state.isPlaying = true
        }).catch((error) => {
          console.error('Error playing audio:', error)
        })
      })
    },
    
    stopAudio: () => {
      set((state) => {
        if (state.currentAudio) {
          state.currentAudio.pause()
          state.currentAudio.currentTime = 0
          state.currentAudio = null
        }
        state.isPlaying = false
      })
    },
    
    setMasterVolume: (volume) => {
      set((state) => {
        if (state.currentAudio) {
          state.currentAudio.volume = volume
        }
      })
    },
    
    addAudioLayer: (layer) => {
      // Implementation for layered audio
      console.log('Adding audio layer:', layer)
    },
    
    removeAudioLayer: (id) => {
      // Implementation for removing audio layer
      console.log('Removing audio layer:', id)
    },
    
    setCurrentSection: (section) => {
      set((state) => {
        state.currentSection = section
      })
    },
    
    reset: () => {
      set((state) => {
        // Stop any playing audio
        if (state.currentAudio) {
          state.currentAudio.pause()
          state.currentAudio = null
        }
        
        // Reset to initial state
        Object.assign(state, initialState)
      })
    },
  }))
)
