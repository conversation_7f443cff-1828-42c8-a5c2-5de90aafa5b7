import { interpolateColor, hexToRgb, rgbToHex } from '@/lib/utils'

export interface ColorPalette {
  name: string
  primary: string
  secondary: string
  accent: string
  background: string
  text: string
  gradient: {
    start: string
    end: string
    direction: string
  }
  mood: 'happy' | 'sad' | 'contemplative' | 'energetic' | 'peaceful' | 'mysterious'
}

export interface ColorScheme {
  light: ColorPalette
  dark: ColorPalette
}

export const predefinedPalettes: Record<string, ColorScheme> = {
  ocean: {
    light: {
      name: 'Ocean Light',
      primary: '#0ea5e9',
      secondary: '#06b6d4',
      accent: '#8b5cf6',
      background: '#f0f9ff',
      text: '#0c4a6e',
      gradient: { start: '#0ea5e9', end: '#06b6d4', direction: '135deg' },
      mood: 'peaceful'
    },
    dark: {
      name: 'Ocean Dark',
      primary: '#38bdf8',
      secondary: '#22d3ee',
      accent: '#a78bfa',
      background: '#0c1426',
      text: '#e0f2fe',
      gradient: { start: '#1e40af', end: '#1e3a8a', direction: '135deg' },
      mood: 'peaceful'
    }
  },
  sunset: {
    light: {
      name: 'Sunset Light',
      primary: '#f97316',
      secondary: '#eab308',
      accent: '#ec4899',
      background: '#fff7ed',
      text: '#9a3412',
      gradient: { start: '#f97316', end: '#eab308', direction: '45deg' },
      mood: 'energetic'
    },
    dark: {
      name: 'Sunset Dark',
      primary: '#fb923c',
      secondary: '#fbbf24',
      accent: '#f472b6',
      background: '#1c1917',
      text: '#fed7aa',
      gradient: { start: '#dc2626', end: '#7c2d12', direction: '45deg' },
      mood: 'energetic'
    }
  },
  forest: {
    light: {
      name: 'Forest Light',
      primary: '#16a34a',
      secondary: '#65a30d',
      accent: '#0891b2',
      background: '#f0fdf4',
      text: '#14532d',
      gradient: { start: '#16a34a', end: '#65a30d', direction: '90deg' },
      mood: 'peaceful'
    },
    dark: {
      name: 'Forest Dark',
      primary: '#4ade80',
      secondary: '#84cc16',
      accent: '#06b6d4',
      background: '#0f1419',
      text: '#dcfce7',
      gradient: { start: '#166534', end: '#365314', direction: '90deg' },
      mood: 'peaceful'
    }
  },
  midnight: {
    light: {
      name: 'Midnight Light',
      primary: '#6366f1',
      secondary: '#8b5cf6',
      accent: '#ec4899',
      background: '#faf7ff',
      text: '#4338ca',
      gradient: { start: '#6366f1', end: '#8b5cf6', direction: '180deg' },
      mood: 'mysterious'
    },
    dark: {
      name: 'Midnight Dark',
      primary: '#818cf8',
      secondary: '#a78bfa',
      accent: '#f472b6',
      background: '#0f0f23',
      text: '#e0e7ff',
      gradient: { start: '#312e81', end: '#581c87', direction: '180deg' },
      mood: 'mysterious'
    }
  },
  autumn: {
    light: {
      name: 'Autumn Light',
      primary: '#dc2626',
      secondary: '#ea580c',
      accent: '#d97706',
      background: '#fefbf3',
      text: '#7f1d1d',
      gradient: { start: '#dc2626', end: '#ea580c', direction: '225deg' },
      mood: 'contemplative'
    },
    dark: {
      name: 'Autumn Dark',
      primary: '#f87171',
      secondary: '#fb923c',
      accent: '#fbbf24',
      background: '#1f1611',
      text: '#fecaca',
      gradient: { start: '#991b1b', end: '#9a3412', direction: '225deg' },
      mood: 'contemplative'
    }
  }
}

export class ColorPaletteManager {
  private currentPalette: ColorPalette
  private isDarkMode: boolean = false

  constructor(paletteName: string = 'ocean', darkMode: boolean = false) {
    this.isDarkMode = darkMode
    this.currentPalette = this.getPalette(paletteName)
  }

  getPalette(name: string): ColorPalette {
    const scheme = predefinedPalettes[name] || predefinedPalettes.ocean
    return this.isDarkMode ? scheme.dark : scheme.light
  }

  setPalette(name: string): void {
    this.currentPalette = this.getPalette(name)
  }

  setDarkMode(enabled: boolean): void {
    const currentName = this.getCurrentPaletteName()
    this.isDarkMode = enabled
    if (currentName) {
      this.currentPalette = this.getPalette(currentName)
    }
  }

  getCurrentPalette(): ColorPalette {
    return this.currentPalette
  }

  getCurrentPaletteName(): string | null {
    for (const [name, scheme] of Object.entries(predefinedPalettes)) {
      if (scheme.light.name === this.currentPalette.name || 
          scheme.dark.name === this.currentPalette.name) {
        return name
      }
    }
    return null
  }

  // Generate color variations
  generateShades(color: string, steps: number = 5): string[] {
    const shades: string[] = []
    const rgb = hexToRgb(color)
    if (!rgb) return [color]

    for (let i = 0; i < steps; i++) {
      const factor = i / (steps - 1)
      const newR = Math.round(rgb.r * (1 - factor * 0.8))
      const newG = Math.round(rgb.g * (1 - factor * 0.8))
      const newB = Math.round(rgb.b * (1 - factor * 0.8))
      shades.push(rgbToHex(newR, newG, newB))
    }

    return shades
  }

  generateTints(color: string, steps: number = 5): string[] {
    const tints: string[] = []
    const rgb = hexToRgb(color)
    if (!rgb) return [color]

    for (let i = 0; i < steps; i++) {
      const factor = i / (steps - 1)
      const newR = Math.round(rgb.r + (255 - rgb.r) * factor * 0.8)
      const newG = Math.round(rgb.g + (255 - rgb.g) * factor * 0.8)
      const newB = Math.round(rgb.b + (255 - rgb.b) * factor * 0.8)
      tints.push(rgbToHex(newR, newG, newB))
    }

    return tints
  }

  // Create complementary colors
  getComplementaryColor(color: string): string {
    const rgb = hexToRgb(color)
    if (!rgb) return color

    const compR = 255 - rgb.r
    const compG = 255 - rgb.g
    const compB = 255 - rgb.b

    return rgbToHex(compR, compG, compB)
  }

  // Create analogous colors
  getAnalogousColors(color: string): string[] {
    // This is a simplified version - in reality, you'd convert to HSL
    const rgb = hexToRgb(color)
    if (!rgb) return [color]

    const analogous: string[] = []
    
    // Shift hue by ±30 degrees (approximated in RGB)
    analogous.push(rgbToHex(
      Math.min(255, rgb.r + 30),
      Math.max(0, rgb.g - 15),
      rgb.b
    ))
    
    analogous.push(color)
    
    analogous.push(rgbToHex(
      Math.max(0, rgb.r - 30),
      Math.min(255, rgb.g + 15),
      rgb.b
    ))

    return analogous
  }

  // Generate gradient CSS
  generateGradientCSS(
    colors: string[], 
    direction: string = '45deg',
    type: 'linear' | 'radial' = 'linear'
  ): string {
    if (type === 'radial') {
      return `radial-gradient(circle, ${colors.join(', ')})`
    }
    return `linear-gradient(${direction}, ${colors.join(', ')})`
  }

  // Apply palette to CSS variables
  applyCSSVariables(): void {
    const root = document.documentElement
    const palette = this.currentPalette

    root.style.setProperty('--color-primary', palette.primary)
    root.style.setProperty('--color-secondary', palette.secondary)
    root.style.setProperty('--color-accent', palette.accent)
    root.style.setProperty('--color-background', palette.background)
    root.style.setProperty('--color-text', palette.text)
    root.style.setProperty('--gradient-primary', 
      this.generateGradientCSS([palette.gradient.start, palette.gradient.end], palette.gradient.direction)
    )
  }

  // Get mood-based effects
  getMoodEffects(): {
    textShadow: string
    boxShadow: string
    filter: string
  } {
    const mood = this.currentPalette.mood

    switch (mood) {
      case 'happy':
        return {
          textShadow: '0 0 10px rgba(255, 255, 0, 0.3)',
          boxShadow: '0 4px 20px rgba(255, 193, 7, 0.3)',
          filter: 'brightness(1.1) saturate(1.2)'
        }
      case 'sad':
        return {
          textShadow: '0 0 5px rgba(0, 0, 255, 0.2)',
          boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',
          filter: 'brightness(0.9) saturate(0.8)'
        }
      case 'energetic':
        return {
          textShadow: '0 0 15px rgba(255, 0, 0, 0.4)',
          boxShadow: '0 6px 25px rgba(255, 0, 0, 0.2)',
          filter: 'brightness(1.2) contrast(1.1)'
        }
      case 'peaceful':
        return {
          textShadow: '0 0 8px rgba(0, 255, 0, 0.2)',
          boxShadow: '0 3px 15px rgba(0, 255, 0, 0.1)',
          filter: 'brightness(1.05) saturate(0.9)'
        }
      case 'mysterious':
        return {
          textShadow: '0 0 12px rgba(128, 0, 128, 0.5)',
          boxShadow: '0 5px 20px rgba(128, 0, 128, 0.3)',
          filter: 'brightness(0.95) contrast(1.2)'
        }
      default:
        return {
          textShadow: 'none',
          boxShadow: 'none',
          filter: 'none'
        }
    }
  }
}
