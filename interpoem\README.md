# Interpoem - Interactive Poetry Platform

A cutting-edge platform for creating immersive, multimedia-rich poetry experiences with advanced typography, sound, and interactive elements.

## Features

### 🎨 Advanced Typography & Calligraphy
- Multiple font layers and gradient text
- Textured fills (gold leaf, watercolor effects)
- Curved, flowing text paths that match your poem's rhythm
- Text animations (fade in, letter-by-letter reveal, typewriter effect)

### 🖼️ Visual Embedding
- Replace letters or words with images without breaking line spacing
- Apply image textures inside text
- Interactive words that react on hover (color changes, hidden art reveals)

### 🎨 Color & Shade Design
- Color palettes per stanza or verse
- Background gradients that shift with mood or tone
- Shadow/light effects to evoke depth or emotion

### 🖼️ Image & Art Integration
- Place illustrations between verses
- Transparent overlays (ink splashes, torn paper effects)
- Blend images into letterforms

### 🎵 Sound & Voice
- Attach audio to words and stanzas
- Ambient sound effects triggered by specific words
- Poem reading playback with synchronized text highlighting
- Layered soundscapes with multiple ambient layers

### ⚡ Immersive Interaction
- Scroll-triggered animations (words drift, fall, bloom)
- Parallax effects with different movement speeds
- Clickable verses revealing hidden content
- Branching poems where reading order changes meaning

### 📤 Publishing & Sharing
- Export to interactive web pages, videos, or print PDFs
- Tablet and VR compatibility for deep immersion
- Website embedding and app publishing options

## Tech Stack

- **Framework**: Next.js 14 with TypeScript
- **Styling**: Tailwind CSS with custom animations
- **Animations**: Framer Motion + GSAP
- **3D Graphics**: Three.js with React Three Fiber
- **Audio**: Howler.js + Tone.js for advanced audio
- **State Management**: Zustand with Immer
- **Typography**: Custom font loading and SVG text masking

## Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd interpoem
```

2. Install dependencies:
```bash
npm install
# or
yarn install
```

3. Start the development server:
```bash
npm run dev
# or
yarn dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Syntax Guide

### Variables & Logic
```
<<set mood="contemplative">>
<<set color="blue-gradient">>

<<if mood=="contemplative">>
  Content shown when mood is contemplative
<</if>>
```

### Media Elements
```
[[audio:ambient-morning.mp3]]
[[image:sunrise.jpg overlay="watercolor"]]
[[delay:2000]]
```

### Interactive Elements
```
[[hover:reveal="Hidden message"]]Hover over this text[[/hover]]
[[style:color="gold" font="script"]]Styled text[[/style]]
```

### Typography Effects
```
*Italic text*
**Bold text**
# Large heading
## Medium heading
```

## Project Structure

```
interpoem/
├── src/
│   ├── app/                 # Next.js app directory
│   ├── components/          # React components
│   │   ├── ui/             # Base UI components
│   │   ├── PoemEditor.tsx  # Poem editing interface
│   │   └── PoemRenderer.tsx # Poem display engine
│   ├── lib/                # Utility libraries
│   ├── stores/             # Zustand state stores
│   ├── types/              # TypeScript type definitions
│   └── hooks/              # Custom React hooks
├── public/                 # Static assets
│   ├── audio/             # Audio files
│   ├── images/            # Image assets
│   └── textures/          # Texture files
└── docs/                  # Documentation
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Roadmap

- [ ] Advanced typography system with curved text paths
- [ ] 3D text animations and effects
- [ ] Voice synthesis integration
- [ ] VR/AR compatibility
- [ ] Blockchain/NFT integration
- [ ] Collaborative editing features
- [ ] Mobile app versions

## Support

For support, email <EMAIL> or join our Discord community.
