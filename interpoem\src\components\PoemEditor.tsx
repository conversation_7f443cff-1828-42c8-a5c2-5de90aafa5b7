'use client'

import { useState, useCallback, useRef, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Button } from '@/components/ui/Button'
import { cn, debounce } from '@/lib/utils'
import { 
  Bold, 
  Italic, 
  Palette, 
  Music, 
  Image, 
  Clock, 
  MousePointer,
  Save,
  Download,
  Play
} from 'lucide-react'

interface PoemEditorProps {
  value: string
  onChange: (value: string) => void
  className?: string
}

const PoemEditor: React.FC<PoemEditorProps> = ({ value, onChange, className }) => {
  const [cursorPosition, setCursorPosition] = useState(0)
  const [selectedText, setSelectedText] = useState('')
  const [showSyntaxHelp, setShowSyntaxHelp] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  // Debounced onChange to prevent excessive updates
  const debouncedOnChange = useCallback(
    debounce((newValue: string) => onChange(newValue), 300),
    [onChange]
  )

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value
    debouncedOnChange(newValue)
  }

  const handleSelectionChange = () => {
    if (textareaRef.current) {
      const start = textareaRef.current.selectionStart
      const end = textareaRef.current.selectionEnd
      setCursorPosition(start)
      setSelectedText(value.substring(start, end))
    }
  }

  const insertSyntax = (syntax: string, wrapSelection = false) => {
    if (!textareaRef.current) return

    const start = textareaRef.current.selectionStart
    const end = textareaRef.current.selectionEnd
    const selectedText = value.substring(start, end)
    
    let newText: string
    let newCursorPos: number

    if (wrapSelection && selectedText) {
      newText = value.substring(0, start) + syntax.replace('{}', selectedText) + value.substring(end)
      newCursorPos = start + syntax.indexOf('{}') + selectedText.length
    } else {
      newText = value.substring(0, start) + syntax + value.substring(end)
      newCursorPos = start + syntax.length
    }

    onChange(newText)
    
    // Set cursor position after update
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.focus()
        textareaRef.current.setSelectionRange(newCursorPos, newCursorPos)
      }
    }, 0)
  }

  const syntaxButtons = [
    {
      icon: Bold,
      label: 'Variable',
      syntax: '<<set name="value">>',
      description: 'Set a variable'
    },
    {
      icon: Italic,
      label: 'Condition',
      syntax: '<<if condition>>\n{}\n<</if>>',
      description: 'Conditional content',
      wrapSelection: true
    },
    {
      icon: Music,
      label: 'Audio',
      syntax: '[[audio:filename.mp3]]',
      description: 'Add audio'
    },
    {
      icon: Image,
      label: 'Image',
      syntax: '[[image:filename.jpg]]',
      description: 'Add image'
    },
    {
      icon: Clock,
      label: 'Delay',
      syntax: '[[delay:2000]]',
      description: 'Add delay (ms)'
    },
    {
      icon: MousePointer,
      label: 'Hover',
      syntax: '[[hover:reveal="hidden text"]]{}[[/hover]]',
      description: 'Hover effect',
      wrapSelection: true
    },
    {
      icon: Palette,
      label: 'Style',
      syntax: '[[style:color="blue" font="serif"]]{}[[/style]]',
      description: 'Text styling',
      wrapSelection: true
    }
  ]

  // Syntax highlighting (basic)
  const highlightSyntax = (text: string) => {
    return text
      .replace(/(&lt;&lt;[^&]*&gt;&gt;)/g, '<span class="text-blue-600 font-semibold">$1</span>')
      .replace(/(\[\[[^\]]*\]\])/g, '<span class="text-green-600 font-semibold">$1</span>')
      .replace(/(#[^\n]*)/g, '<span class="text-purple-600 font-bold">$1</span>')
      .replace(/(\*[^*]*\*)/g, '<span class="text-orange-600 italic">$1</span>')
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Toolbar */}
      <div className="flex flex-wrap gap-2 p-4 bg-muted/50 rounded-lg border">
        <div className="flex items-center gap-2 mr-4">
          <span className="text-sm font-medium text-muted-foreground">Quick Insert:</span>
        </div>
        
        {syntaxButtons.map((button, index) => (
          <Button
            key={index}
            variant="outline"
            size="sm"
            onClick={() => insertSyntax(button.syntax, button.wrapSelection)}
            className="flex items-center gap-1"
            title={button.description}
          >
            <button.icon className="w-3 h-3" />
            <span className="hidden sm:inline">{button.label}</span>
          </Button>
        ))}
        
        <div className="ml-auto flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowSyntaxHelp(!showSyntaxHelp)}
          >
            Help
          </Button>
          <Button variant="outline" size="sm">
            <Save className="w-3 h-3 mr-1" />
            Save
          </Button>
          <Button variant="outline" size="sm">
            <Download className="w-3 h-3 mr-1" />
            Export
          </Button>
        </div>
      </div>

      {/* Syntax Help Panel */}
      {showSyntaxHelp && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4"
        >
          <h4 className="font-semibold mb-3 text-blue-900 dark:text-blue-100">Syntax Reference</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h5 className="font-medium mb-2">Variables & Logic</h5>
              <code className="block bg-white dark:bg-slate-800 p-2 rounded text-xs">
                {`<<set mood="happy">>`}<br/>
                {`<<if mood=="happy">>`}<br/>
                {`  Content when happy`}<br/>
                {`<</if>>`}
              </code>
            </div>
            <div>
              <h5 className="font-medium mb-2">Media & Effects</h5>
              <code className="block bg-white dark:bg-slate-800 p-2 rounded text-xs">
                {`[[audio:ambient.mp3]]`}<br/>
                {`[[image:sunset.jpg]]`}<br/>
                {`[[delay:3000]]`}<br/>
                {`[[hover:reveal="surprise!"]]text[[/hover]]`}
              </code>
            </div>
          </div>
        </motion.div>
      )}

      {/* Editor */}
      <div className="relative">
        <textarea
          ref={textareaRef}
          value={value}
          onChange={handleTextChange}
          onSelect={handleSelectionChange}
          onKeyUp={handleSelectionChange}
          onClick={handleSelectionChange}
          className={cn(
            "w-full h-96 p-4 border rounded-lg resize-none",
            "font-mono text-sm leading-relaxed",
            "focus:outline-none focus:ring-2 focus:ring-blue-500",
            "bg-background text-foreground",
            "placeholder:text-muted-foreground"
          )}
          placeholder="Write your interactive poem here...

Example:
# My Interactive Poem

<<set mood='contemplative'>>

In the quiet of the morning,
[[audio:birds.mp3]]
When the world is still asleep,
*I find my thoughts are forming*
[[hover:reveal='Like whispers in the deep']]Words that dance and leap.

<<if mood=='contemplative'>>
Each line a gentle meditation,
Each word a sacred prayer.
<</if>>

[[delay:2000]]
And in this digital creation,
Poetry fills the air."
        />
        
        {/* Status Bar */}
        <div className="absolute bottom-2 right-2 text-xs text-muted-foreground bg-background/80 px-2 py-1 rounded">
          Line: {value.substring(0, cursorPosition).split('\n').length} | 
          Char: {cursorPosition} |
          {selectedText && ` Selected: ${selectedText.length}`}
        </div>
      </div>

      {/* Preview Button */}
      <div className="flex justify-center">
        <Button className="flex items-center gap-2">
          <Play className="w-4 h-4" />
          Preview Poem
        </Button>
      </div>
    </div>
  )
}

export default PoemEditor
