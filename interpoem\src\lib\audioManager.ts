import { AudioLayer, SoundscapeConfig } from '@/types/poem'

export class AudioManager {
  private audioContext: AudioContext | null = null
  private masterGain: GainNode | null = null
  private layers: Map<string, {
    audio: HTMLAudioElement
    source: MediaElementAudioSourceNode
    gain: GainNode
    config: AudioLayer
  }> = new Map()
  private isInitialized = false

  constructor() {
    this.initialize()
  }

  private async initialize() {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      this.masterGain = this.audioContext.createGain()
      this.masterGain.connect(this.audioContext.destination)
      this.isInitialized = true
    } catch (error) {
      console.error('Failed to initialize audio context:', error)
    }
  }

  async addLayer(layer: AudioLayer): Promise<void> {
    if (!this.isInitialized || !this.audioContext || !this.masterGain) {
      await this.initialize()
    }

    if (!this.audioContext || !this.masterGain) {
      throw new Error('Audio context not available')
    }

    try {
      // Create audio element
      const audio = new Audio(`/audio/${layer.src}`)
      audio.loop = layer.loop
      audio.crossOrigin = 'anonymous'

      // Create audio nodes
      const source = this.audioContext.createMediaElementSource(audio)
      const gain = this.audioContext.createGain()

      // Connect nodes
      source.connect(gain)
      gain.connect(this.masterGain)

      // Set initial volume
      gain.gain.value = layer.volume

      // Store layer
      this.layers.set(layer.id, {
        audio,
        source,
        gain,
        config: layer
      })

      // Handle fade in
      if (layer.fadeIn) {
        gain.gain.setValueAtTime(0, this.audioContext.currentTime)
        gain.gain.linearRampToValueAtTime(
          layer.volume,
          this.audioContext.currentTime + layer.fadeIn / 1000
        )
      }

      console.log(`Audio layer ${layer.id} added successfully`)
    } catch (error) {
      console.error(`Failed to add audio layer ${layer.id}:`, error)
      throw error
    }
  }

  async playLayer(layerId: string): Promise<void> {
    const layer = this.layers.get(layerId)
    if (!layer) {
      throw new Error(`Audio layer ${layerId} not found`)
    }

    try {
      // Resume audio context if suspended
      if (this.audioContext?.state === 'suspended') {
        await this.audioContext.resume()
      }

      await layer.audio.play()
      console.log(`Playing audio layer ${layerId}`)
    } catch (error) {
      console.error(`Failed to play audio layer ${layerId}:`, error)
      throw error
    }
  }

  pauseLayer(layerId: string): void {
    const layer = this.layers.get(layerId)
    if (!layer) {
      throw new Error(`Audio layer ${layerId} not found`)
    }

    layer.audio.pause()
    console.log(`Paused audio layer ${layerId}`)
  }

  stopLayer(layerId: string): void {
    const layer = this.layers.get(layerId)
    if (!layer) {
      throw new Error(`Audio layer ${layerId} not found`)
    }

    layer.audio.pause()
    layer.audio.currentTime = 0
    console.log(`Stopped audio layer ${layerId}`)
  }

  removeLayer(layerId: string): void {
    const layer = this.layers.get(layerId)
    if (!layer) return

    // Fade out if configured
    if (layer.config.fadeOut && this.audioContext) {
      const fadeOutTime = layer.config.fadeOut / 1000
      layer.gain.gain.linearRampToValueAtTime(
        0,
        this.audioContext.currentTime + fadeOutTime
      )

      setTimeout(() => {
        layer.audio.pause()
        layer.source.disconnect()
        layer.gain.disconnect()
        this.layers.delete(layerId)
      }, layer.config.fadeOut)
    } else {
      layer.audio.pause()
      layer.source.disconnect()
      layer.gain.disconnect()
      this.layers.delete(layerId)
    }

    console.log(`Removed audio layer ${layerId}`)
  }

  setLayerVolume(layerId: string, volume: number): void {
    const layer = this.layers.get(layerId)
    if (!layer) {
      throw new Error(`Audio layer ${layerId} not found`)
    }

    layer.gain.gain.setValueAtTime(volume, this.audioContext?.currentTime || 0)
    layer.config.volume = volume
  }

  setMasterVolume(volume: number): void {
    if (this.masterGain) {
      this.masterGain.gain.setValueAtTime(volume, this.audioContext?.currentTime || 0)
    }
  }

  async loadSoundscape(config: SoundscapeConfig): Promise<void> {
    // Clear existing layers
    this.clearAllLayers()

    // Set master volume
    this.setMasterVolume(config.masterVolume)

    // Add all layers
    for (const layer of config.layers) {
      await this.addLayer(layer)
    }

    // Apply spatial audio if configured
    if (config.spatialAudio && this.audioContext) {
      this.setupSpatialAudio()
    }

    // Apply reverb if configured
    if (config.reverb && this.audioContext) {
      this.setupReverb(config.reverb)
    }
  }

  private setupSpatialAudio(): void {
    if (!this.audioContext) return

    // Create panner nodes for spatial audio
    this.layers.forEach((layer, layerId) => {
      const panner = this.audioContext!.createPanner()
      panner.panningModel = 'HRTF'
      panner.distanceModel = 'inverse'
      panner.refDistance = 1
      panner.maxDistance = 10000
      panner.rolloffFactor = 1
      panner.coneInnerAngle = 360
      panner.coneOuterAngle = 0
      panner.coneOuterGain = 0

      // Disconnect and reconnect with panner
      layer.source.disconnect()
      layer.source.connect(panner)
      panner.connect(layer.gain)

      // Set random position for demo
      const angle = Math.random() * Math.PI * 2
      const distance = Math.random() * 5 + 1
      panner.positionX.setValueAtTime(Math.cos(angle) * distance, this.audioContext!.currentTime)
      panner.positionY.setValueAtTime(0, this.audioContext!.currentTime)
      panner.positionZ.setValueAtTime(Math.sin(angle) * distance, this.audioContext!.currentTime)
    })
  }

  private setupReverb(reverbConfig: { roomSize: number; dampening: number }): void {
    if (!this.audioContext || !this.masterGain) return

    // Create convolver for reverb
    const convolver = this.audioContext.createConvolver()
    const reverbGain = this.audioContext.createGain()

    // Generate impulse response
    const sampleRate = this.audioContext.sampleRate
    const length = sampleRate * reverbConfig.roomSize
    const impulse = this.audioContext.createBuffer(2, length, sampleRate)

    for (let channel = 0; channel < 2; channel++) {
      const channelData = impulse.getChannelData(channel)
      for (let i = 0; i < length; i++) {
        const decay = Math.pow(1 - i / length, reverbConfig.dampening)
        channelData[i] = (Math.random() * 2 - 1) * decay
      }
    }

    convolver.buffer = impulse

    // Connect reverb
    reverbGain.gain.value = 0.3
    this.masterGain.connect(convolver)
    convolver.connect(reverbGain)
    reverbGain.connect(this.audioContext.destination)
  }

  clearAllLayers(): void {
    this.layers.forEach((_, layerId) => {
      this.removeLayer(layerId)
    })
  }

  getLayerInfo(layerId: string) {
    const layer = this.layers.get(layerId)
    if (!layer) return null

    return {
      id: layerId,
      isPlaying: !layer.audio.paused,
      currentTime: layer.audio.currentTime,
      duration: layer.audio.duration,
      volume: layer.config.volume,
      loop: layer.config.loop
    }
  }

  getAllLayers() {
    return Array.from(this.layers.keys()).map(id => this.getLayerInfo(id)).filter(Boolean)
  }

  cleanup(): void {
    this.clearAllLayers()
    if (this.audioContext) {
      this.audioContext.close()
    }
  }
}
