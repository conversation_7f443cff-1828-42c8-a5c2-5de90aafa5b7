'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Play, Pause, Volume2, VolumeX } from 'lucide-react'
import { cn } from '@/lib/utils'
import { MediaElement as MediaElementType } from '@/types/poem'

interface MediaElementProps {
  media: MediaElementType
  className?: string
  autoPlay?: boolean
  onLoad?: () => void
  onError?: (error: string) => void
}

const MediaElement: React.FC<MediaElementProps> = ({
  media,
  className,
  autoPlay = false,
  onLoad,
  onError
}) => {
  const [isPlaying, setIsPlaying] = useState(false)
  const [isLoaded, setIsLoaded] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [volume, setVolume] = useState(media.volume || 1)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [showControls, setShowControls] = useState(false)

  const audioRef = useRef<HTMLAudioElement>(null)
  const videoRef = useRef<HTMLVideoElement>(null)

  useEffect(() => {
    if (autoPlay && media.autoplay !== false) {
      handlePlay()
    }
  }, [autoPlay, media.autoplay])

  const handlePlay = async () => {
    try {
      if (media.type === 'audio' && audioRef.current) {
        await audioRef.current.play()
        setIsPlaying(true)
      } else if (media.type === 'video' && videoRef.current) {
        await videoRef.current.play()
        setIsPlaying(true)
      }
    } catch (error) {
      console.error('Error playing media:', error)
      onError?.('Failed to play media')
    }
  }

  const handlePause = () => {
    if (media.type === 'audio' && audioRef.current) {
      audioRef.current.pause()
    } else if (media.type === 'video' && videoRef.current) {
      videoRef.current.pause()
    }
    setIsPlaying(false)
  }

  const handleVolumeChange = (newVolume: number) => {
    setVolume(newVolume)
    if (audioRef.current) {
      audioRef.current.volume = newVolume
    }
    if (videoRef.current) {
      videoRef.current.volume = newVolume
    }
  }

  const handleMute = () => {
    setIsMuted(!isMuted)
    if (audioRef.current) {
      audioRef.current.muted = !isMuted
    }
    if (videoRef.current) {
      videoRef.current.muted = !isMuted
    }
  }

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  // Render image with overlay effects
  const renderImage = () => (
    <motion.div
      className={cn("relative overflow-hidden rounded-lg", className)}
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
      whileHover={{ scale: 1.02 }}
    >
      <img
        src={`/images/${media.src}`}
        alt={media.alt || 'Poem illustration'}
        className="w-full h-auto object-cover"
        onLoad={() => {
          setIsLoaded(true)
          onLoad?.()
        }}
        onError={() => onError?.('Failed to load image')}
      />
      
      {/* Overlay effects */}
      {media.overlay && (
        <div 
          className={cn(
            "absolute inset-0 pointer-events-none",
            media.overlay === 'watercolor' && "bg-gradient-to-br from-blue-200/30 to-purple-200/30",
            media.overlay === 'ink' && "bg-gradient-to-t from-black/20 to-transparent",
            media.overlay === 'paper' && "bg-texture-paper opacity-20"
          )}
          style={{
            backgroundImage: media.overlay.includes('.') ? `url(/textures/${media.overlay})` : undefined,
            mixBlendMode: 'multiply'
          }}
        />
      )}
    </motion.div>
  )

  // Render audio player
  const renderAudio = () => (
    <motion.div
      className={cn(
        "audio-player bg-muted/50 rounded-lg p-4 border",
        "hover:bg-muted/70 transition-colors",
        className
      )}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      onMouseEnter={() => setShowControls(true)}
      onMouseLeave={() => setShowControls(false)}
    >
      <audio
        ref={audioRef}
        src={`/audio/${media.src}`}
        loop={media.loop}
        onLoadedData={() => {
          setIsLoaded(true)
          setDuration(audioRef.current?.duration || 0)
          onLoad?.()
        }}
        onTimeUpdate={() => {
          setCurrentTime(audioRef.current?.currentTime || 0)
        }}
        onEnded={() => setIsPlaying(false)}
        onError={() => onError?.('Failed to load audio')}
      />

      <div className="flex items-center gap-3">
        {/* Play/Pause Button */}
        <button
          onClick={isPlaying ? handlePause : handlePlay}
          className="w-10 h-10 bg-primary text-primary-foreground rounded-full flex items-center justify-center hover:bg-primary/90 transition-colors"
          disabled={!isLoaded}
        >
          {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4 ml-0.5" />}
        </button>

        {/* Progress Bar */}
        <div className="flex-1">
          <div className="w-full bg-muted-foreground/20 rounded-full h-2">
            <motion.div
              className="bg-primary h-2 rounded-full"
              style={{ width: `${(currentTime / duration) * 100}%` }}
              initial={{ width: 0 }}
              animate={{ width: `${(currentTime / duration) * 100}%` }}
            />
          </div>
          <div className="flex justify-between text-xs text-muted-foreground mt-1">
            <span>{formatTime(currentTime)}</span>
            <span>{formatTime(duration)}</span>
          </div>
        </div>

        {/* Volume Controls */}
        <AnimatePresence>
          {showControls && (
            <motion.div
              className="flex items-center gap-2"
              initial={{ opacity: 0, width: 0 }}
              animate={{ opacity: 1, width: 'auto' }}
              exit={{ opacity: 0, width: 0 }}
            >
              <button
                onClick={handleMute}
                className="p-1 hover:bg-muted rounded"
              >
                {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
              </button>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={volume}
                onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
                className="w-16"
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Audio Visualizer */}
      {isPlaying && (
        <motion.div
          className="audio-visualizer flex items-end justify-center gap-1 mt-3 h-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        >
          {[...Array(12)].map((_, i) => (
            <motion.div
              key={i}
              className="bg-primary rounded-full"
              style={{ width: '3px' }}
              animate={{ 
                height: [4, Math.random() * 20 + 8, 4],
              }}
              transition={{ 
                duration: 0.5 + Math.random() * 0.5,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          ))}
        </motion.div>
      )}
    </motion.div>
  )

  // Render video player
  const renderVideo = () => (
    <motion.div
      className={cn("video-player relative rounded-lg overflow-hidden", className)}
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
    >
      <video
        ref={videoRef}
        src={`/video/${media.src}`}
        loop={media.loop}
        muted={isMuted}
        className="w-full h-auto"
        onLoadedData={() => {
          setIsLoaded(true)
          setDuration(videoRef.current?.duration || 0)
          onLoad?.()
        }}
        onTimeUpdate={() => {
          setCurrentTime(videoRef.current?.currentTime || 0)
        }}
        onEnded={() => setIsPlaying(false)}
        onError={() => onError?.('Failed to load video')}
      />

      {/* Video Controls Overlay */}
      <div className="absolute inset-0 bg-black/20 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center">
        <button
          onClick={isPlaying ? handlePause : handlePlay}
          className="w-16 h-16 bg-black/50 text-white rounded-full flex items-center justify-center hover:bg-black/70 transition-colors"
        >
          {isPlaying ? <Pause className="w-8 h-8" /> : <Play className="w-8 h-8 ml-1" />}
        </button>
      </div>
    </motion.div>
  )

  switch (media.type) {
    case 'image':
      return renderImage()
    case 'audio':
      return renderAudio()
    case 'video':
      return renderVideo()
    default:
      return null
  }
}

export default MediaElement
