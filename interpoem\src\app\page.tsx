'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import PoemEditor from '@/components/PoemEditor'
import Poem<PERSON>enderer from '@/components/PoemRenderer'
import { Button } from '@/components/ui/Button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/Tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import ColorPaletteSelector from '@/components/ColorPaletteSelector'
import BackgroundEffects from '@/components/BackgroundEffects'
import { Palette, Music, Type, Sparkles } from 'lucide-react'

const samplePoem = `# The Digital Dawn

<<set mood="contemplative">>
<<set color="blue-gradient">>

In the quiet hours before dawn,
[[audio:ambient-morning.mp3]]
When pixels dance on glowing screens,
And code becomes our modern dreams.

<<if mood=="contemplative">>
*Each line of text, a whispered thought,*
[[hover:reveal="In silence, wisdom is often caught"]]
*Each function call, a prayer to time,*
*Each variable, a reason, rhyme.*
<</if>>

[[image:src="digital-sunrise.jpg" overlay="watercolor"]]

The cursor blinks—a heartbeat's pace,
[[delay:2000]]
In this electric, sacred space.
Where poetry meets technology,
And art transcends reality.

<<set mood="hopeful">>
<<set color="gold-gradient">>

*Rise, digital dawn, rise bright and true,*
*Paint the world in ones and zeros blue.*
*For in this code, we find our voice,*
*In algorithms, we make our choice.*`

export default function HomePage() {
  const [currentPoem, setCurrentPoem] = useState(samplePoem)
  const [activeTab, setActiveTab] = useState('editor')
  const [backgroundType, setBackgroundType] = useState<'particles' | 'waves' | 'gradient' | 'geometric' | 'none'>('particles')
  const [backgroundColors, setBackgroundColors] = useState(['#0ea5e9', '#06b6d4', '#8b5cf6'])

  const handlePaletteChange = (paletteName: string, darkMode: boolean) => {
    // Update background colors based on palette
    // This is a simplified version - you could make it more sophisticated
    switch (paletteName) {
      case 'ocean':
        setBackgroundColors(['#0ea5e9', '#06b6d4', '#8b5cf6'])
        break
      case 'sunset':
        setBackgroundColors(['#f97316', '#eab308', '#ec4899'])
        break
      case 'forest':
        setBackgroundColors(['#16a34a', '#65a30d', '#0891b2'])
        break
      case 'midnight':
        setBackgroundColors(['#6366f1', '#8b5cf6', '#ec4899'])
        break
      case 'autumn':
        setBackgroundColors(['#dc2626', '#ea580c', '#d97706'])
        break
      default:
        setBackgroundColors(['#0ea5e9', '#06b6d4', '#8b5cf6'])
    }
  }

  return (
    <div className="min-h-screen relative">
      {/* Background Effects */}
      <BackgroundEffects
        type={backgroundType}
        intensity="low"
        colors={backgroundColors}
        interactive={true}
      />
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm dark:bg-slate-900/80">
        <div className="container mx-auto px-4 py-6">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center justify-between"
          >
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                <Sparkles className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                  Interpoem
                </h1>
                <p className="text-sm text-muted-foreground">Interactive Poetry Platform</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Type className="w-4 h-4 mr-2" />
                Typography
              </Button>
              <ColorPaletteSelector onPaletteChange={handlePaletteChange} />
              <Button variant="outline" size="sm">
                <Music className="w-4 h-4 mr-2" />
                Audio
              </Button>
            </div>
          </motion.div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-8">
              <TabsTrigger value="editor">Editor</TabsTrigger>
              <TabsTrigger value="preview">Preview</TabsTrigger>
            </TabsList>

            <TabsContent value="editor" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Poem Editor</CardTitle>
                  <CardDescription>
                    Write your interactive poem using our enhanced syntax. 
                    Use variables, conditions, media, and interactive elements.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <PoemEditor
                    value={currentPoem}
                    onChange={setCurrentPoem}
                  />
                </CardContent>
              </Card>

              {/* Syntax Help */}
              <Card>
                <CardHeader>
                  <CardTitle>Syntax Guide</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <h4 className="font-semibold mb-2">Variables & Logic</h4>
                      <code className="block bg-muted p-2 rounded">
                        {`<<set mood="happy">>`}<br/>
                        {`<<if mood=="happy">>`}<br/>
                        {`  Happy content here`}<br/>
                        {`<</if>>`}
                      </code>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">Media & Effects</h4>
                      <code className="block bg-muted p-2 rounded">
                        {`[[audio:song.mp3]]`}<br/>
                        {`[[image:pic.jpg]]`}<br/>
                        {`[[delay:2000]]`}<br/>
                        {`[[hover:reveal="text"]]`}
                      </code>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="preview" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Live Preview</CardTitle>
                  <CardDescription>
                    See your interactive poem come to life with all effects and animations.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <PoemRenderer poem={currentPoem} />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </motion.div>
      </main>
    </div>
  )
}
