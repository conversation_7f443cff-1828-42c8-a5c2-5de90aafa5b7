'use client'

import { useEffect, useRef, useState } from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'

interface BackgroundEffectsProps {
  type: 'particles' | 'waves' | 'gradient' | 'geometric' | 'none'
  intensity: 'low' | 'medium' | 'high'
  colors: string[]
  className?: string
  interactive?: boolean
}

const BackgroundEffects: React.FC<BackgroundEffectsProps> = ({
  type,
  intensity,
  colors,
  className,
  interactive = false
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationRef = useRef<number>()
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight
    }
    resizeCanvas()
    window.addEventListener('resize', resizeCanvas)

    // Mouse tracking for interactive effects
    const handleMouseMove = (e: MouseEvent) => {
      if (interactive) {
        setMousePosition({ x: e.clientX, y: e.clientY })
      }
    }
    window.addEventListener('mousemove', handleMouseMove)

    // Animation loop
    let particles: any[] = []
    let time = 0

    const initializeEffect = () => {
      switch (type) {
        case 'particles':
          initializeParticles()
          break
        case 'waves':
          // Waves don't need initialization
          break
        case 'geometric':
          initializeGeometric()
          break
      }
    }

    const initializeParticles = () => {
      const particleCount = intensity === 'low' ? 50 : intensity === 'medium' ? 100 : 200
      particles = []
      
      for (let i = 0; i < particleCount; i++) {
        particles.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          vx: (Math.random() - 0.5) * 2,
          vy: (Math.random() - 0.5) * 2,
          size: Math.random() * 3 + 1,
          color: colors[Math.floor(Math.random() * colors.length)],
          opacity: Math.random() * 0.5 + 0.1
        })
      }
    }

    const initializeGeometric = () => {
      const shapeCount = intensity === 'low' ? 10 : intensity === 'medium' ? 20 : 40
      particles = []
      
      for (let i = 0; i < shapeCount; i++) {
        particles.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          rotation: Math.random() * Math.PI * 2,
          rotationSpeed: (Math.random() - 0.5) * 0.02,
          size: Math.random() * 50 + 20,
          color: colors[Math.floor(Math.random() * colors.length)],
          opacity: Math.random() * 0.3 + 0.1,
          shape: Math.floor(Math.random() * 3) // 0: triangle, 1: square, 2: circle
        })
      }
    }

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      time += 0.01

      switch (type) {
        case 'particles':
          animateParticles()
          break
        case 'waves':
          animateWaves()
          break
        case 'geometric':
          animateGeometric()
          break
      }

      animationRef.current = requestAnimationFrame(animate)
    }

    const animateParticles = () => {
      particles.forEach((particle) => {
        // Update position
        particle.x += particle.vx
        particle.y += particle.vy

        // Interactive effect
        if (interactive) {
          const dx = mousePosition.x - particle.x
          const dy = mousePosition.y - particle.y
          const distance = Math.sqrt(dx * dx + dy * dy)
          
          if (distance < 100) {
            particle.x -= dx * 0.01
            particle.y -= dy * 0.01
          }
        }

        // Wrap around edges
        if (particle.x < 0) particle.x = canvas.width
        if (particle.x > canvas.width) particle.x = 0
        if (particle.y < 0) particle.y = canvas.height
        if (particle.y > canvas.height) particle.y = 0

        // Draw particle
        ctx.save()
        ctx.globalAlpha = particle.opacity
        ctx.fillStyle = particle.color
        ctx.beginPath()
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
        ctx.fill()
        ctx.restore()
      })
    }

    const animateWaves = () => {
      const waveCount = intensity === 'low' ? 3 : intensity === 'medium' ? 5 : 8
      
      for (let i = 0; i < waveCount; i++) {
        const amplitude = 50 + i * 20
        const frequency = 0.01 + i * 0.005
        const phase = time + i * Math.PI / 3
        
        ctx.save()
        ctx.globalAlpha = 0.1 + i * 0.05
        ctx.strokeStyle = colors[i % colors.length]
        ctx.lineWidth = 2
        ctx.beginPath()
        
        for (let x = 0; x <= canvas.width; x += 5) {
          const y = canvas.height / 2 + Math.sin(x * frequency + phase) * amplitude
          if (x === 0) {
            ctx.moveTo(x, y)
          } else {
            ctx.lineTo(x, y)
          }
        }
        
        ctx.stroke()
        ctx.restore()
      }
    }

    const animateGeometric = () => {
      particles.forEach((shape) => {
        // Update rotation
        shape.rotation += shape.rotationSpeed

        // Interactive effect
        if (interactive) {
          const dx = mousePosition.x - shape.x
          const dy = mousePosition.y - shape.y
          const distance = Math.sqrt(dx * dx + dy * dy)
          
          if (distance < 150) {
            shape.rotationSpeed += 0.001
          } else {
            shape.rotationSpeed *= 0.99
          }
        }

        // Draw shape
        ctx.save()
        ctx.globalAlpha = shape.opacity
        ctx.fillStyle = shape.color
        ctx.translate(shape.x, shape.y)
        ctx.rotate(shape.rotation)
        
        switch (shape.shape) {
          case 0: // Triangle
            ctx.beginPath()
            ctx.moveTo(0, -shape.size / 2)
            ctx.lineTo(-shape.size / 2, shape.size / 2)
            ctx.lineTo(shape.size / 2, shape.size / 2)
            ctx.closePath()
            ctx.fill()
            break
          case 1: // Square
            ctx.fillRect(-shape.size / 2, -shape.size / 2, shape.size, shape.size)
            break
          case 2: // Circle
            ctx.beginPath()
            ctx.arc(0, 0, shape.size / 2, 0, Math.PI * 2)
            ctx.fill()
            break
        }
        
        ctx.restore()
      })
    }

    if (type !== 'none' && type !== 'gradient') {
      initializeEffect()
      animate()
    }

    return () => {
      window.removeEventListener('resize', resizeCanvas)
      window.removeEventListener('mousemove', handleMouseMove)
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [type, intensity, colors, interactive, mousePosition])

  if (type === 'none') {
    return null
  }

  if (type === 'gradient') {
    return (
      <motion.div
        className={cn("fixed inset-0 -z-10", className)}
        style={{
          background: `linear-gradient(45deg, ${colors.join(', ')})`
        }}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
      />
    )
  }

  return (
    <canvas
      ref={canvasRef}
      className={cn("fixed inset-0 -z-10 pointer-events-none", className)}
      style={{ opacity: intensity === 'low' ? 0.3 : intensity === 'medium' ? 0.5 : 0.7 }}
    />
  )
}

export default BackgroundEffects
