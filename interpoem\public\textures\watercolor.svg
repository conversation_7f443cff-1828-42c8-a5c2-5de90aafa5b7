<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter id="watercolor">
      <feTurbulence baseFrequency="0.04" numOctaves="3" result="noise"/>
      <feDisplacementMap in="SourceGraphic" in2="noise" scale="8"/>
      <feGaussianBlur stdDeviation="2" result="blur"/>
      <feColorMatrix type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 0.8 0"/>
    </filter>
  </defs>
  <rect width="100%" height="100%" fill="url(#watercolorGradient)" filter="url(#watercolor)"/>
  <defs>
    <linearGradient id="watercolorGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.6" />
      <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:0.4" />
      <stop offset="100%" style="stop-color:#06b6d4;stop-opacity:0.6" />
    </linearGradient>
  </defs>
</svg>
