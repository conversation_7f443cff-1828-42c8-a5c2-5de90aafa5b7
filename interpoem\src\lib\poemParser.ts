import { 
  PoemStructure, 
  VerseElement, 
  LineElement, 
  WordElement, 
  PoemCommand,
  TextStyle,
  MediaElement,
  InteractiveElement
} from '@/types/poem'
import { generateId } from '@/lib/utils'

export class PoemParser {
  private variables: Record<string, any> = {}
  private currentStyle: TextStyle = {}

  parse(poemText: string): PoemStructure {
    // Reset state
    this.variables = {}
    this.currentStyle = {}

    const lines = poemText.split('\n')
    const verses: VerseElement[] = []
    let currentVerse: LineElement[] = []
    let title: string | undefined

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()
      
      // Skip empty lines but use them as verse separators
      if (!line) {
        if (currentVerse.length > 0) {
          verses.push(this.createVerse(currentVerse))
          currentVerse = []
        }
        continue
      }

      // Handle title (first # heading)
      if (line.startsWith('#') && !title) {
        title = line.replace(/^#+\s*/, '')
        continue
      }

      // Process line
      const processedLine = this.processLine(line, i)
      if (processedLine) {
        currentVerse.push(processedLine)
      }
    }

    // Add final verse if exists
    if (currentVerse.length > 0) {
      verses.push(this.createVerse(currentVerse))
    }

    return {
      title,
      verses,
      globalStyle: this.currentStyle
    }
  }

  private processLine(line: string, lineIndex: number): LineElement | null {
    // Handle commands
    if (line.includes('<<') || line.includes('[[')) {
      const commands = this.extractCommands(line)
      
      // Process set commands immediately
      for (const command of commands) {
        if (command.type === 'set') {
          this.handleSetCommand(command)
        }
      }

      // Check if line should be rendered after command processing
      const cleanLine = this.removeCommands(line)
      if (!cleanLine.trim()) {
        return null // Command-only line
      }
      
      line = cleanLine
    }

    // Handle conditional rendering
    if (!this.shouldRenderLine(line)) {
      return null
    }

    // Create line element
    const words = this.parseWords(line, lineIndex)
    
    return {
      id: generateId(),
      words,
      alignment: this.detectAlignment(line),
      style: { ...this.currentStyle }
    }
  }

  private extractCommands(line: string): PoemCommand[] {
    const commands: PoemCommand[] = []
    
    // Extract << >> commands
    const setMatches = line.matchAll(/<<(\w+)\s+([^>]+)>>/g)
    for (const match of setMatches) {
      const [, type, params] = match
      commands.push({
        type: type as any,
        params: this.parseParams(params)
      })
    }

    // Extract [[ ]] commands
    const mediaMatches = line.matchAll(/\[\[([^\]]+)\]\]/g)
    for (const match of mediaMatches) {
      const [, content] = match
      const [type, ...params] = content.split(':')
      commands.push({
        type: type as any,
        params: { src: params.join(':'), ...this.parseParams(content) }
      })
    }

    return commands
  }

  private parseParams(paramString: string): Record<string, any> {
    const params: Record<string, any> = {}
    
    // Handle key="value" format
    const keyValueMatches = paramString.matchAll(/(\w+)="([^"]+)"/g)
    for (const match of keyValueMatches) {
      const [, key, value] = match
      params[key] = value
    }

    // Handle simple value format (for media)
    if (Object.keys(params).length === 0 && paramString.includes(':')) {
      const [type, src] = paramString.split(':')
      params.type = type
      params.src = src
    }

    return params
  }

  private removeCommands(line: string): string {
    return line
      .replace(/<<[^>]+>>/g, '')
      .replace(/\[\[[^\]]+\]\]/g, '')
      .trim()
  }

  private handleSetCommand(command: PoemCommand): void {
    if (command.type === 'set') {
      Object.entries(command.params).forEach(([key, value]) => {
        this.variables[key] = value
        
        // Update current style based on variable
        if (key === 'color') {
          this.currentStyle.color = value as string
        } else if (key === 'font') {
          this.currentStyle.fontFamily = value as string
        }
      })
    }
  }

  private shouldRenderLine(line: string): boolean {
    // Check for conditional statements
    const ifMatch = line.match(/<<if\s+(.+?)>>/)
    if (ifMatch) {
      return this.evaluateCondition(ifMatch[1])
    }
    
    return true
  }

  private evaluateCondition(condition: string): boolean {
    // Simple condition evaluation (variable=="value")
    const match = condition.match(/(\w+)=="([^"]+)"/)
    if (match) {
      const [, variable, value] = match
      return this.variables[variable] === value
    }
    
    return true
  }

  private parseWords(line: string, lineIndex: number): WordElement[] {
    const words: WordElement[] = []
    
    // Handle hover effects
    const hoverRegex = /\[\[hover:reveal="([^"]+)"\]\]([^[]+)\[\[\/hover\]\]/g
    line = line.replace(hoverRegex, (match, revealText, wordText) => {
      const wordId = generateId()
      words.push({
        id: wordId,
        text: wordText.trim(),
        position: { line: lineIndex, word: words.length },
        interactive: {
          type: 'hover',
          content: revealText
        }
      })
      return `__WORD_${wordId}__`
    })

    // Handle style tags
    const styleRegex = /\[\[style:([^\]]+)\]\]([^[]+)\[\[\/style\]\]/g
    line = line.replace(styleRegex, (match, styleParams, wordText) => {
      const wordId = generateId()
      const style = this.parseStyleParams(styleParams)
      words.push({
        id: wordId,
        text: wordText.trim(),
        position: { line: lineIndex, word: words.length },
        style
      })
      return `__WORD_${wordId}__`
    })

    // Handle italic text
    line = line.replace(/\*([^*]+)\*/g, (match, text) => {
      const wordId = generateId()
      words.push({
        id: wordId,
        text: text,
        position: { line: lineIndex, word: words.length },
        style: { fontStyle: 'italic', color: 'rgb(37 99 235)' }
      })
      return `__WORD_${wordId}__`
    })

    // Split remaining text into words
    const remainingWords = line.split(/\s+/).filter(word => word.trim())
    
    for (const word of remainingWords) {
      if (word.startsWith('__WORD_') && word.endsWith('__')) {
        // Find the pre-processed word
        const wordId = word.replace(/__WORD_|__/g, '')
        const existingWord = words.find(w => w.id === wordId)
        if (existingWord) {
          existingWord.position.word = words.filter(w => w.position.line === lineIndex).length
        }
      } else {
        // Regular word
        words.push({
          id: generateId(),
          text: word,
          position: { line: lineIndex, word: words.filter(w => w.position.line === lineIndex).length },
          style: { ...this.currentStyle }
        })
      }
    }

    return words
  }

  private parseStyleParams(styleParams: string): TextStyle {
    const style: TextStyle = {}
    const params = this.parseParams(styleParams)
    
    if (params.color) style.color = params.color
    if (params.font) style.fontFamily = params.font
    if (params.size) style.fontSize = params.size
    if (params.weight) style.fontWeight = params.weight
    
    return style
  }

  private detectAlignment(line: string): 'left' | 'center' | 'right' | 'justify' {
    if (line.startsWith('#')) return 'center'
    return 'left'
  }

  private createVerse(lines: LineElement[]): VerseElement {
    return {
      id: generateId(),
      lines,
      style: { ...this.currentStyle }
    }
  }
}
