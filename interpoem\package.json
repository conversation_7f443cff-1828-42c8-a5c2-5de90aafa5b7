{"name": "interpoem", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "typescript": "^5.3.3", "framer-motion": "^10.16.16", "gsap": "^3.12.2", "three": "^0.159.0", "@react-three/fiber": "^8.15.12", "@react-three/drei": "^9.92.7", "howler": "^2.2.4", "tone": "^14.7.77", "@types/howler": "^2.2.11", "zustand": "^4.4.7", "immer": "^10.0.3", "canvas-confetti": "^1.9.2", "lottie-react": "^2.4.0", "react-markdown": "^9.0.1", "remark": "^15.0.1", "remark-gfm": "^4.0.0", "rehype-raw": "^7.0.0", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0", "class-variance-authority": "^0.7.0", "lucide-react": "^0.303.0", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "react-use": "^17.4.2", "use-sound": "^4.0.1"}, "devDependencies": {"eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "tailwindcss": "^3.4.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@tailwindcss/typography": "^0.5.10", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/aspect-ratio": "^0.4.2", "@types/three": "^0.159.0", "@types/canvas-confetti": "^1.6.4"}}